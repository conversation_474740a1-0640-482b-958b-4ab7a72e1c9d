document.addEventListener('DOMContentLoaded', async () => {
  // Tab management
  const tabs = document.querySelectorAll('.tab');
  const tabContents = document.querySelectorAll('.tab-content');

  // Overview tab elements
  const currentUser = document.getElementById('currentUser');
  const documentId = document.getElementById('documentId');
  const documentTitle = document.getElementById('documentTitle');
  const refreshBtn = document.getElementById('refreshBtn');
  const fetchAllRevisionsBtn = document.getElementById('fetchAllRevisionsBtn');
  const revisionOverview = document.getElementById('revisionOverview');

  // Revisions tab elements
  const analyzeRevisionsBtn = document.getElementById('analyzeRevisionsBtn');
  const showTimelineBtn = document.getElementById('showTimelineBtn');
  const revisionSummary = document.getElementById('revisionSummary');
  const revisionDetails = document.getElementById('revisionDetails');
  const revisionTimeline = document.getElementById('revisionTimeline');

  // Export tab elements
  const exportComprehensiveBtn = document.getElementById('exportComprehensiveBtn');
  const exportUserSummaryBtn = document.getElementById('exportUserSummaryBtn');
  const exportTimelineBtn = document.getElementById('exportTimelineBtn');
  const exportJsonBtn = document.getElementById('exportJsonBtn');
  const exportStatus = document.getElementById('exportStatus');

  // Global state
  let currentDocumentMetadata = null;
  let currentRevisionData = null;
  let currentAnalysisResults = null;
  let currentCsvData = null;

  // Tab switching functionality
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const targetTab = tab.getAttribute('data-tab');

      // Update tab buttons
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');

      // Update tab content
      tabContents.forEach(content => {
        content.classList.remove('active');
        if (content.id === targetTab) {
          content.classList.add('active');
        }
      });
    });
  });

  // Get current tab and document information
  async function updateStatus() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (tab.url.includes('docs.google.com/document')) {
        chrome.tabs.sendMessage(tab.id, { action: 'getStatus' }, (response) => {
          if (chrome.runtime.lastError) {
            revisionOverview.innerHTML = '<div style="color: #ea4335;">Extension not loaded on this page</div>';
            return;
          }

          if (response) {
            currentUser.textContent = response.user || 'Unknown';
            documentId.textContent = response.documentId || 'Unknown';

            if (response.hasRevisionData) {
              revisionOverview.innerHTML = '<div style="color: #34a853;">✅ Revision data available - Ready for analysis</div>';
            }
          }
        });

        // Get document metadata
        chrome.tabs.sendMessage(tab.id, { action: 'getDocumentMetadata' }, (metadata) => {
          if (!chrome.runtime.lastError && metadata) {
            currentDocumentMetadata = metadata;
            documentTitle.textContent = metadata.title || 'Unknown Document';
            documentId.textContent = metadata.id || 'Unknown';
          }
        });
      } else {
        revisionOverview.innerHTML = '<div style="color: #ea4335;">Please open a Google Docs document</div>';
        currentUser.textContent = 'N/A';
        documentId.textContent = 'N/A';
        documentTitle.textContent = 'N/A';
      }
    } catch (error) {
      console.error('Error updating status:', error);
    }
  }

  // Fetch ALL revisions with comprehensive pagination
  async function fetchAllRevisions() {
    fetchAllRevisionsBtn.textContent = 'Fetching ALL Revisions...';
    fetchAllRevisionsBtn.disabled = true;

    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      chrome.tabs.sendMessage(tab.id, { action: 'fetchAllRevisionData' }, (response) => {
        fetchAllRevisionsBtn.textContent = 'Fetch ALL Revisions';
        fetchAllRevisionsBtn.disabled = false;

        if (chrome.runtime.lastError) {
          revisionOverview.innerHTML = '<div style="color: #ea4335;">Error: Extension not loaded</div>';
          return;
        }

        if (response.success) {
          currentRevisionData = response.data;
          displayRevisionOverview(response.data);

          // Update export status
          exportStatus.innerHTML = `
            <div>📊 <strong>Revision Data Loaded Successfully!</strong></div>
            <div style="margin-top: 10px; font-size: 13px;">
              • <strong>Total Revisions:</strong> ${response.data.totalRevisions || response.data.tileInfo?.length || 0}<br>
              • <strong>Contributors:</strong> ${Object.keys(response.data.userMap || {}).length}<br>
              • <strong>Ready for export</strong> - Use buttons above to download data
            </div>
          `;
        } else {
          revisionOverview.innerHTML = `<div style="color: #ea4335;">Error: ${response.error}</div>`;
        }
      });
    } catch (error) {
      fetchAllRevisionsBtn.textContent = 'Fetch ALL Revisions';
      fetchAllRevisionsBtn.disabled = false;
      revisionOverview.innerHTML = `<div style="color: #ea4335;">Error: ${error.message}</div>`;
    }
  }

  function displayRevisionOverview(data) {
    if (!data || !data.tileInfo) {
      revisionOverview.innerHTML = '<div style="color: #ea4335;">No revision data available</div>';
      return;
    }

    const revisionCount = data.tileInfo.length;
    const userCount = Object.keys(data.userMap || {}).length;

    // Get date range
    const dates = data.tileInfo.map(r => new Date(r.endMillis)).sort((a, b) => a - b);
    const dateRange = dates.length > 0 ?
      `${dates[0].toLocaleDateString()} to ${dates[dates.length - 1].toLocaleDateString()}` :
      'Unknown';

    revisionOverview.innerHTML = `
      <div style="color: #34a853;"><strong>✅ ALL Revisions Loaded Successfully!</strong></div>
      <div style="margin-top: 10px;">
        <div><strong>📝 Total Revisions:</strong> ${revisionCount}</div>
        <div><strong>👥 Contributors:</strong> ${userCount}</div>
        <div><strong>📅 Date Range:</strong> ${dateRange}</div>
      </div>
      <div style="margin-top: 10px; font-size: 12px; color: #5f6368;">
        Go to "All Revisions" tab to analyze data or "Export Data" tab to download CSV files
      </div>
    `;
  }

  // Load and display time summary
  async function loadTimeSummary() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['timeEntries'], (result) => {
        const entries = result.timeEntries || [];
        resolve(entries);
      });
    });
  }

  function formatDuration(milliseconds) {
    const hours = Math.floor(milliseconds / 3600000);
    const minutes = Math.floor((milliseconds % 3600000) / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  function formatDurationHours(milliseconds) {
    const hours = (milliseconds / 3600000).toFixed(2);
    return `${hours}h`;
  }

  async function displayTimeSummary() {
    const data = await loadTimeSummary();
    const entries = data.entries || [];
    const revisionHistory = data.revisionHistory || [];
    
    if (entries.length === 0 && revisionHistory.length === 0) {
      timeSummary.innerHTML = '<div style="text-align: center; color: #5f6368;">No time entries or revisions yet</div>';
      totalTime.textContent = 'Total: 0h 0m';
      return;
    }

    // Group by user
    const userSummary = {};
    let totalDuration = 0;

    // Process time entries
    entries.forEach(entry => {
      if (!userSummary[entry.user]) {
        userSummary[entry.user] = {
          totalTime: 0,
          sessions: 0,
          revisions: 0
        };
      }
      userSummary[entry.user].totalTime += entry.duration;
      userSummary[entry.user].sessions += 1;
      totalDuration += entry.duration;
    });
    
    // Process revision history
    revisionHistory.forEach(revision => {
      if (!userSummary[revision.user]) {
        userSummary[revision.user] = {
          totalTime: 0,
          sessions: 0,
          revisions: 0
        };
      }
      userSummary[revision.user].revisions += 1;
    });

    // Display user summary with revision counts
    let summaryHtml = '';
    Object.entries(userSummary)
      .sort((a, b) => b[1].totalTime - a[1].totalTime)
      .forEach(([user, data]) => {
        const percentage = data.totalTime > 0 ? 
          ((data.totalTime / totalDuration) * 100).toFixed(1) : 0;
        
        summaryHtml += `
          <div class="time-entry">
            <div>
              <div class="user-name">${user}</div>
              <div style="font-size: 12px; color: #5f6368;">
                ${data.sessions} sessions • ${percentage}% • ${data.revisions} edits
              </div>
            </div>
            <div class="time-duration">${formatDuration(data.totalTime)}</div>
          </div>
        `;
      });

    timeSummary.innerHTML = summaryHtml;
    
    const totalHours = Math.floor(totalDuration / 3600000);
    const totalMinutes = Math.floor((totalDuration % 3600000) / 60000);
    totalTime.textContent = `Total: ${totalHours}h ${totalMinutes}m`;
  }

  // Export functions
  function exportData() {
    loadTimeSummary().then(entries => {
      const dataStr = JSON.stringify(entries, null, 2);
      downloadFile(dataStr, 'time-tracking-data.json', 'application/json');
    });
  }

  function exportCsv() {
    loadTimeSummary().then(data => {
      const entries = data.entries || [];
      const revisionHistory = data.revisionHistory || [];
      
      if (entries.length === 0 && revisionHistory.length === 0) {
        alert('No data to export');
        return;
      }

      // Group by user for payment calculation
      const userSummary = {};
      
      // Process time entries
      entries.forEach(entry => {
        if (!userSummary[entry.user]) {
          userSummary[entry.user] = {
            totalTime: 0,
            sessions: 0,
            revisions: 0,
            firstSession: entry.timestamp,
            lastSession: entry.timestamp
          };
        }
        userSummary[entry.user].totalTime += entry.duration;
        userSummary[entry.user].sessions += 1;
        
        if (entry.timestamp < userSummary[entry.user].firstSession) {
          userSummary[entry.user].firstSession = entry.timestamp;
        }
        if (entry.timestamp > userSummary[entry.user].lastSession) {
          userSummary[entry.user].lastSession = entry.timestamp;
        }
      });
      
      // Process revision history
      revisionHistory.forEach(revision => {
        if (!userSummary[revision.user]) {
          userSummary[revision.user] = {
            totalTime: 0,
            sessions: 0,
            revisions: 0,
            firstSession: revision.timestamp,
            lastSession: revision.timestamp
          };
        }
        userSummary[revision.user].revisions += 1;
        
        if (revision.timestamp < userSummary[revision.user].firstSession) {
          userSummary[revision.user].firstSession = revision.timestamp;
        }
        if (revision.timestamp > userSummary[revision.user].lastSession) {
          userSummary[revision.user].lastSession = revision.timestamp;
        }
      });

      // Create CSV content
      let csvContent = 'User,Total Hours,Total Minutes,Sessions,Revisions,First Activity,Last Activity,Payment (Hours)\n';
      
      Object.entries(userSummary).forEach(([user, data]) => {
        const totalHours = (data.totalTime / 3600000).toFixed(2);
        const totalMinutes = Math.floor(data.totalTime / 60000);
        const firstSession = new Date(data.firstSession).toLocaleString();
        const lastSession = new Date(data.lastSession).toLocaleString();
        
        csvContent += `"${user}",${totalHours},${totalMinutes},${data.sessions},${data.revisions},"${firstSession}","${lastSession}",${totalHours}\n`;
      });

      downloadFile(csvContent, 'time-tracking-payment.csv', 'text/csv');
    });
  }

  function downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  // New export functions
  function exportUnifiedCsv() {
    if (!currentUnifiedReport) {
      alert('Please generate a unified report first');
      return;
    }

    let csvContent = 'User Name,Time Tracked (Hours),Sessions,Revisions,Characters,Contribution Score,Rank,Recommendation\n';

    currentUnifiedReport.unified.users.forEach((user, index) => {
      const timeHours = (user.timeTracked / (1000 * 60 * 60)).toFixed(2);
      const recommendation = currentUnifiedReport.unified.recommendations[index];
      csvContent += `"${user.name}",${timeHours},${user.timeSessions},${user.revisions},${user.characters},${user.contributionScore.toFixed(1)},${index + 1},"${recommendation?.recommendation || 'N/A'}"\n`;
    });

    const timestamp = new Date().toISOString().split('T')[0];
    const docTitle = currentDocumentMetadata?.title || 'document';
    downloadFile(csvContent, `${docTitle}_unified_report_${timestamp}.csv`, 'text/csv');
  }

  function exportUnifiedJson() {
    if (!currentUnifiedReport) {
      alert('Please generate a unified report first');
      return;
    }

    const jsonContent = JSON.stringify(currentUnifiedReport, null, 2);
    const timestamp = new Date().toISOString().split('T')[0];
    const docTitle = currentDocumentMetadata?.title || 'document';
    downloadFile(jsonContent, `${docTitle}_full_report_${timestamp}.json`, 'application/json');
  }

  function exportTimeData() {
    loadTimeSummary().then(entries => {
      const dataStr = JSON.stringify(entries, null, 2);
      const timestamp = new Date().toISOString().split('T')[0];
      const docTitle = currentDocumentMetadata?.title || 'document';
      downloadFile(dataStr, `${docTitle}_time_data_${timestamp}.json`, 'application/json');
    });
  }

  function clearData() {
    if (confirm('Are you sure you want to clear all time tracking data? This cannot be undone.')) {
      chrome.storage.local.remove(['timeEntries'], () => {
        displayTimeSummary();
        alert('Data cleared successfully');
      });
    }
  }

  // Comprehensive revision analysis
  async function analyzeAllRevisions() {
    if (!currentRevisionData) {
      revisionSummary.innerHTML = '<div style="color: #ea4335;">Please fetch revision data first from the Overview tab</div>';
      return;
    }

    analyzeRevisionsBtn.textContent = 'Analyzing...';
    analyzeRevisionsBtn.disabled = true;

    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      chrome.tabs.sendMessage(tab.id, { action: 'analyzeRevisions' }, (response) => {
        analyzeRevisionsBtn.textContent = 'Analyze All Revisions';
        analyzeRevisionsBtn.disabled = false;

        if (chrome.runtime.lastError) {
          revisionSummary.innerHTML = '<div style="color: #ea4335;">Error: Extension not loaded</div>';
          return;
        }

        if (response.success) {
          currentAnalysisResults = response.report;
          currentCsvData = response.csvData;
          displayDetailedAnalysis(response.report);
        } else {
          revisionSummary.innerHTML = `<div style="color: #ea4335;">Analysis Error: ${response.error}</div>`;
        }
      });
    } catch (error) {
      analyzeRevisionsBtn.textContent = 'Analyze All Revisions';
      analyzeRevisionsBtn.disabled = false;
      revisionSummary.innerHTML = `<div style="color: #ea4335;">Error: ${error.message}</div>`;
    }
  }

  function displayDetailedAnalysis(report) {
    // Update summary
    revisionSummary.innerHTML = `
      <div style="color: #34a853;"><strong>✅ Analysis Complete!</strong></div>
      <div style="margin-top: 10px;">
        <div><strong>📝 Revision Objects:</strong> ${report.summary.totalRevisions}</div>
        <div><strong>🔄 User Contributions:</strong> ${report.summary.totalUserContributions}</div>
        <div><strong>👥 Active Contributors:</strong> ${report.summary.totalUsers}</div>
        <div><strong>📅 Document Period:</strong> ${report.summary.documentPeriod.duration}</div>
      </div>
    `;

    // Show detailed user analysis
    let detailsHtml = '<h4>👥 Contributor Analysis</h4>';

    report.userDetails.forEach((user, index) => {
      detailsHtml += `
        <div class="revision-user">
          <div>
            <div class="user-name" style="color: ${user.color || '#1a73e8'}">${user.name}</div>
            <div class="user-contribution">
              ${user.totalRevisions} revisions (${user.contributionPercentage}%) • ${user.totalCharacters.toLocaleString()} characters
            </div>
            <div style="font-size: 11px; color: #5f6368; margin-top: 4px;">
              First: ${user.firstActivity} | Last: ${user.lastActivity}
            </div>
          </div>
          <div style="text-align: right;">
            <div style="font-weight: bold; color: #1a73e8;">#${index + 1}</div>
            <div style="font-size: 11px; color: #5f6368;">${user.workingSessions} sessions</div>
          </div>
        </div>
      `;
    });

    revisionDetails.innerHTML = detailsHtml;
    revisionDetails.style.display = 'block';
  }

  function analyzeRevisions() {
    if (!currentRevisionData) {
      revisionDetails.innerHTML = '<div style="color: #ea4335;">Please fetch revision data first</div>';
      revisionDetails.style.display = 'block';
      return;
    }

    analyzeRevisionsBtn.textContent = 'Analyzing...';
    analyzeRevisionsBtn.disabled = true;

    try {
      // Use the RevisionAnalyzer (assuming it's available globally)
      const analyzer = new RevisionAnalyzer();
      const analysisResults = analyzer.parseRevisionData(currentRevisionData);
      const report = analyzer.generateReport();

      displayRevisionAnalysis(report);

      analyzeRevisionsBtn.textContent = 'Analyze Revisions';
      analyzeRevisionsBtn.disabled = false;
    } catch (error) {
      revisionDetails.innerHTML = `<div style="color: #ea4335;">Analysis Error: ${error.message}</div>`;
      revisionDetails.style.display = 'block';
      analyzeRevisionsBtn.textContent = 'Analyze Revisions';
      analyzeRevisionsBtn.disabled = false;
    }
  }

  function showTimeline() {
    if (!currentAnalysisResults) {
      revisionTimeline.innerHTML = '<div style="color: #ea4335;">Please analyze revisions first</div>';
      revisionTimeline.style.display = 'block';
      return;
    }

    let timelineHtml = '<h4>📅 Revision Timeline</h4>';

    // Show first 20 timeline events
    const timelineEvents = currentAnalysisResults.timeline.slice(0, 20);

    timelineEvents.forEach((event, index) => {
      timelineHtml += `
        <div class="revision-user" style="border-left: 3px solid #1a73e8; padding-left: 10px; margin-bottom: 8px;">
          <div>
            <div style="font-weight: bold; font-size: 12px; color: #1a73e8;">
              ${event.timestamp}
            </div>
            <div class="user-contribution">
              <strong>Users:</strong> ${event.users} | <strong>Characters:</strong> ${event.characters} | <strong>Position:</strong> ${event.position}
            </div>
            <div style="font-size: 11px; color: #5f6368;">
              ${event.name}
            </div>
          </div>
        </div>
      `;
    });

    if (currentAnalysisResults.timeline.length > 20) {
      timelineHtml += `<div style="text-align: center; color: #5f6368; margin-top: 15px;">... and ${currentAnalysisResults.timeline.length - 20} more events (export CSV for complete timeline)</div>`;
    }

    revisionTimeline.innerHTML = timelineHtml;
    revisionTimeline.style.display = 'block';
    revisionDetails.style.display = 'none';
  }

  // Unified reporting functions
  async function generateUnifiedReport() {
    generateReportBtn.textContent = 'Generating...';
    generateReportBtn.disabled = true;

    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      chrome.tabs.sendMessage(tab.id, { action: 'generateUnifiedReport' }, (response) => {
        generateReportBtn.textContent = 'Generate Unified Report';
        generateReportBtn.disabled = false;

        if (chrome.runtime.lastError) {
          unifiedReport.innerHTML = '<div style="color: #ea4335;">Error: Extension not loaded</div>';
          return;
        }

        if (response.success) {
          currentUnifiedReport = response.report;
          displayUnifiedReport(response.report);
        } else {
          unifiedReport.innerHTML = `<div style="color: #ea4335;">Error: ${response.error}</div>`;
        }
      });
    } catch (error) {
      generateReportBtn.textContent = 'Generate Unified Report';
      generateReportBtn.disabled = false;
      unifiedReport.innerHTML = `<div style="color: #ea4335;">Error: ${error.message}</div>`;
    }
  }

  function displayUnifiedReport(report) {
    let html = `
      <div class="revision-summary">
        <h4>📋 Unified Analysis Report</h4>
        <div><strong>Document:</strong> ${report.metadata?.title || 'Unknown'}</div>
        <div><strong>Generated:</strong> ${new Date(report.summary.reportGenerated).toLocaleString()}</div>
        <div><strong>Data Quality:</strong> ${report.unified.dataQuality.completeness}% complete</div>
      </div>
    `;

    if (report.unified.users.length > 0) {
      html += '<h4>👥 Contributor Rankings</h4>';

      report.unified.users.forEach((user, index) => {
        const recommendation = report.unified.recommendations[index];
        html += `
          <div class="revision-user">
            <div>
              <div class="user-name">${user.name}</div>
              <div class="user-contribution">
                Score: ${user.contributionScore.toFixed(1)} • ${(user.timeTracked / (1000 * 60 * 60)).toFixed(2)}h tracked • ${user.revisions} revisions
              </div>
              <div style="font-size: 11px; color: #5f6368; margin-top: 4px;">
                ${recommendation?.recommendation || 'No recommendation'}
              </div>
            </div>
            <div style="text-align: right;">
              <div style="font-weight: bold; color: #1a73e8;">#${index + 1}</div>
            </div>
          </div>
        `;
      });
    } else {
      html += '<div style="color: #5f6368; text-align: center; padding: 20px;">No contributor data available</div>';
    }

    unifiedReport.innerHTML = html;
  }

  // Export functions
  function exportComprehensiveCSV() {
    if (!currentCsvData || !currentCsvData.comprehensive) {
      alert('Please analyze revisions first');
      return;
    }

    const timestamp = new Date().toISOString().split('T')[0];
    const docTitle = currentDocumentMetadata?.title || 'document';
    downloadFile(currentCsvData.comprehensive, `${docTitle}_comprehensive_revisions_${timestamp}.csv`, 'text/csv');
  }

  function exportUserSummaryCSV() {
    if (!currentCsvData || !currentCsvData.userSummary) {
      alert('Please analyze revisions first');
      return;
    }

    const timestamp = new Date().toISOString().split('T')[0];
    const docTitle = currentDocumentMetadata?.title || 'document';
    downloadFile(currentCsvData.userSummary, `${docTitle}_user_summary_${timestamp}.csv`, 'text/csv');
  }

  function exportTimelineCSV() {
    if (!currentCsvData || !currentCsvData.timeline) {
      alert('Please analyze revisions first');
      return;
    }

    const timestamp = new Date().toISOString().split('T')[0];
    const docTitle = currentDocumentMetadata?.title || 'document';
    downloadFile(currentCsvData.timeline, `${docTitle}_timeline_${timestamp}.csv`, 'text/csv');
  }

  function exportFullJSON() {
    if (!currentAnalysisResults) {
      alert('Please analyze revisions first');
      return;
    }

    const jsonContent = JSON.stringify({
      metadata: currentDocumentMetadata,
      analysisResults: currentAnalysisResults,
      rawRevisionData: currentRevisionData
    }, null, 2);

    const timestamp = new Date().toISOString().split('T')[0];
    const docTitle = currentDocumentMetadata?.title || 'document';
    downloadFile(jsonContent, `${docTitle}_full_analysis_${timestamp}.json`, 'application/json');
  }

  // Event listeners
  refreshBtn.addEventListener('click', updateStatus);
  fetchAllRevisionsBtn.addEventListener('click', fetchAllRevisions);

  // Revision tab event listeners
  analyzeRevisionsBtn.addEventListener('click', analyzeAllRevisions);
  showTimelineBtn.addEventListener('click', showTimeline);

  // Export tab event listeners
  exportComprehensiveBtn.addEventListener('click', exportComprehensiveCSV);
  exportUserSummaryBtn.addEventListener('click', exportUserSummaryCSV);
  exportTimelineBtn.addEventListener('click', exportTimelineCSV);
  exportJsonBtn.addEventListener('click', exportFullJSON);

  // Initialize
  await updateStatus();
});
