// Enhanced background script for Google Docs Time & Revision Tracker

chrome.runtime.onInstalled.addListener(() => {
  console.log('ecogo.ai Time Tracker extension installed');

  // Initialize storage with enhanced structure
  chrome.storage.local.get(['timeEntries', 'revisionCache', 'documentMetadata'], (result) => {
    const updates = {};

    if (!result.timeEntries) {
      updates.timeEntries = [];
    }

    if (!result.revisionCache) {
      updates.revisionCache = {};
    }

    if (!result.documentMetadata) {
      updates.documentMetadata = {};
    }

    if (Object.keys(updates).length > 0) {
      chrome.storage.local.set(updates);
    }
  });

  // Log successful installation
  console.log('ecogo.ai Time Tracker installed successfully - ready to analyze revisions!');
});

// Listen for tab updates to handle Google Docs navigation
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && 
      tab.url && 
      tab.url.includes('docs.google.com/document')) {
    
    // Inject content script if not already injected
    chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['content.js']
    }).catch(() => {
      // Script might already be injected, ignore error
    });
  }
});

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'saveTimeEntry') {
    // Save time entry to storage with enhanced metadata
    chrome.storage.local.get(['timeEntries'], (result) => {
      const entries = result.timeEntries || [];
      const enhancedEntry = {
        ...request.data,
        tabId: sender.tab?.id,
        url: sender.tab?.url,
        savedAt: new Date().toISOString()
      };
      entries.push(enhancedEntry);
      chrome.storage.local.set({ timeEntries: entries });
    });
  }

  if (request.action === 'saveRevisionData') {
    // Save revision data to cache
    chrome.storage.local.get(['revisionCache'], (result) => {
      const cache = result.revisionCache || {};
      cache[request.documentId] = {
        data: request.data,
        timestamp: Date.now(),
        url: sender.tab?.url
      };
      chrome.storage.local.set({ revisionCache: cache });
    });
  }

  if (request.action === 'saveDocumentMetadata') {
    // Save document metadata
    chrome.storage.local.get(['documentMetadata'], (result) => {
      const metadata = result.documentMetadata || {};
      metadata[request.documentId] = {
        ...request.metadata,
        lastAccessed: new Date().toISOString(),
        tabId: sender.tab?.id
      };
      chrome.storage.local.set({ documentMetadata: metadata });
    });
  }

  if (request.action === 'getStorageData') {
    chrome.storage.local.get(null, (data) => {
      sendResponse(data);
    });
    return true; // Indicates we will send a response asynchronously
  }

  if (request.action === 'revisionDataUpdated') {
    // Notify other tabs/popup that revision data was updated
    chrome.runtime.sendMessage({
      action: 'revisionDataRefresh',
      documentId: request.documentId
    });
  }
});

// Periodic cleanup of old entries (optional)
setInterval(() => {
  chrome.storage.local.get(['timeEntries'], (result) => {
    const entries = result.timeEntries || [];
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    
    // Keep only entries from the last 30 days
    const recentEntries = entries.filter(entry => 
      new Date(entry.timestamp).getTime() > thirtyDaysAgo
    );
    
    if (recentEntries.length !== entries.length) {
      chrome.storage.local.set({ timeEntries: recentEntries });
      console.log(`Cleaned up ${entries.length - recentEntries.length} old entries`);
    }
  });
}, 24 * 60 * 60 * 1000); // Run once per day
