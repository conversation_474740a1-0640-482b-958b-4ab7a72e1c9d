// Logo loading handler for ecogo.ai Time Tracker
document.addEventListener('DOMContentLoaded', function() {
  const logo = document.querySelector('.header .logo');
  if (logo) {
    logo.onerror = function() {
      // If external logo fails to load, hide it and adjust header
      console.warn('External ecogo.ai logo failed to load');
      this.style.display = 'none';
      const headerContent = document.querySelector('.header-content');
      if (headerContent) {
        headerContent.innerHTML = '<h3>📊 ecogo.ai Time Tracker</h3>';
      }
    };

    // Handle successful loading
    logo.onload = function() {
      console.log('ecogo.ai logo loaded successfully from external URL');
    };
  }
});
