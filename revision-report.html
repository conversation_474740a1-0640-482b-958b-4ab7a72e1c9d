<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Docs Revision Analysis Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1a73e8;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #1a73e8;
            padding-bottom: 10px;
        }
        h2 {
            color: #333;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #1a73e8;
            padding-left: 15px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            color: #1a73e8;
            font-size: 2em;
        }
        .summary-card p {
            margin: 0;
            color: #666;
            font-weight: 500;
        }
        .user-card {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .user-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
        }
        .user-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        .user-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .stat-value {
            font-size: 1.3em;
            font-weight: bold;
            color: #1a73e8;
        }
        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        .timeline-item {
            background: #fff;
            border-left: 4px solid #1a73e8;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 0 5px 5px 0;
        }
        .timeline-time {
            font-weight: bold;
            color: #1a73e8;
            margin-bottom: 5px;
        }
        .timeline-users {
            color: #333;
            margin-bottom: 5px;
        }
        .timeline-details {
            font-size: 0.9em;
            color: #666;
        }
        .export-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .btn {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #1557b0;
        }
        .csv-output {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Google Docs Revision Analysis Report</h1>
        
        <div id="loading" class="loading">
            <p>Loading analysis...</p>
        </div>
        
        <div id="error" class="error" style="display: none;">
            <p>Error loading analysis. Please check the console for details.</p>
        </div>
        
        <div id="report-content" style="display: none;">
            <!-- Summary Section -->
            <h2>📈 Document Summary</h2>
            <div class="summary-grid" id="summary-grid">
                <!-- Summary cards will be inserted here -->
            </div>
            
            <!-- User Contributions Section -->
            <h2>👥 User Contributions</h2>
            <div id="user-contributions">
                <!-- User cards will be inserted here -->
            </div>
            
            <!-- Timeline Section -->
            <h2>📅 Revision Timeline</h2>
            <div id="timeline">
                <!-- Timeline items will be inserted here -->
            </div>
            
            <!-- Export Section -->
            <div class="export-section">
                <h2>💾 Export Data</h2>
                <p>Export the analysis data for payment calculation and record keeping:</p>
                <button class="btn" onclick="downloadCSV()">Download Payment CSV</button>
                <button class="btn" onclick="downloadJSON()">Download Full Report JSON</button>
                <button class="btn" onclick="showCSV()">Show CSV Preview</button>
                
                <div id="csv-preview" class="csv-output" style="display: none;">
                    <!-- CSV content will be shown here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Include the analyzer scripts -->
    <script src="revision-analyzer.js"></script>
    <script src="test-revision-analysis.js"></script>
    
    <script>
        let analysisData = null;
        
        // Run analysis when page loads
        document.addEventListener('DOMContentLoaded', function() {
            try {
                analysisData = runRevisionAnalysis();
                if (analysisData) {
                    displayReport(analysisData.report);
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('report-content').style.display = 'block';
                } else {
                    showError();
                }
            } catch (error) {
                console.error('Error running analysis:', error);
                showError();
            }
        });
        
        function showError() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
        }
        
        function displayReport(report) {
            // Display summary
            const summaryGrid = document.getElementById('summary-grid');
            summaryGrid.innerHTML = `
                <div class="summary-card">
                    <h3>${report.summary.totalRevisions}</h3>
                    <p>Total Revisions</p>
                </div>
                <div class="summary-card">
                    <h3>${report.summary.totalUsers}</h3>
                    <p>Contributors</p>
                </div>
                <div class="summary-card">
                    <h3>${report.summary.documentPeriod.duration}</h3>
                    <p>Document Lifespan</p>
                </div>
                <div class="summary-card">
                    <h3>${new Date(report.summary.documentPeriod.start).toLocaleDateString()}</h3>
                    <p>Started</p>
                </div>
            `;
            
            // Display user contributions
            const userContributions = document.getElementById('user-contributions');
            userContributions.innerHTML = report.userDetails.map(user => `
                <div class="user-card">
                    <div class="user-header">
                        <div class="user-name" style="color: ${user.color}">${user.name}</div>
                    </div>
                    <div class="user-stats">
                        <div class="stat">
                            <div class="stat-value">${user.totalRevisions}</div>
                            <div class="stat-label">Revisions (${user.contributionPercentage}%)</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">${user.totalCharacters.toLocaleString()}</div>
                            <div class="stat-label">Characters</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">${user.timeSpent}</div>
                            <div class="stat-label">Time Spent</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">${user.workingSessions}</div>
                            <div class="stat-label">Sessions</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">${user.averageSessionDuration}</div>
                            <div class="stat-label">Avg Session</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px; font-size: 0.9em; color: #666;">
                        <strong>First Activity:</strong> ${user.firstActivity}<br>
                        <strong>Last Activity:</strong> ${user.lastActivity}
                    </div>
                </div>
            `).join('');
            
            // Display timeline (first 10 items)
            const timeline = document.getElementById('timeline');
            timeline.innerHTML = report.timeline.slice(0, 10).map(event => `
                <div class="timeline-item">
                    <div class="timeline-time">${event.timestamp}</div>
                    <div class="timeline-users"><strong>Contributors:</strong> ${event.users}</div>
                    <div class="timeline-details">
                        <strong>Characters:</strong> ${event.characters} | 
                        <strong>Position:</strong> ${event.position} | 
                        <strong>Name:</strong> ${event.name}
                    </div>
                </div>
            `).join('');
            
            if (report.timeline.length > 10) {
                timeline.innerHTML += `<p style="text-align: center; color: #666; margin-top: 20px;">... and ${report.timeline.length - 10} more revision events</p>`;
            }
        }
        
        function downloadCSV() {
            if (!analysisData) return;
            
            const csvContent = analysisData.csvData;
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'google-docs-payment-report.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function downloadJSON() {
            if (!analysisData) return;
            
            const jsonContent = JSON.stringify(analysisData.report, null, 2);
            const blob = new Blob([jsonContent], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'google-docs-full-report.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function showCSV() {
            if (!analysisData) return;
            
            const csvPreview = document.getElementById('csv-preview');
            csvPreview.textContent = analysisData.csvData;
            csvPreview.style.display = csvPreview.style.display === 'none' ? 'block' : 'none';
        }
    </script>
</body>
</html>
