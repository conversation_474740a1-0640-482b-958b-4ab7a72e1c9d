// Comprehensive revision analysis content script
// Prevent duplicate initialization
if (typeof window.revisionTracker !== 'undefined') {
  console.log('Revision tracker already initialized, skipping...');
} else {

let revisionTracker = {
  currentUser: null,
  documentId: null,

  init() {
    this.documentId = this.extractDocumentId();
    this.getCurrentUser();
    this.initializeAPIClient();

    console.log('Revision Tracker initialized for document:', this.documentId);
  },

  initializeAPIClient() {
    if (window.GoogleDocsAPIClient) {
      window.apiClient = new GoogleDocsAPIClient();
      window.apiClient.init();
      console.log('API Client initialized for comprehensive revision analysis');
    }
  },
  
  extractDocumentId() {
    const url = window.location.href;
    const match = url.match(/\/document\/d\/([a-zA-Z0-9-_]+)/);
    return match ? match[1] : null;
  },

  getCurrentUser() {
    // Try to get user from Google account info
    const userElement = document.querySelector('[data-tooltip*="@"]') ||
                       document.querySelector('[aria-label*="@"]');

    if (userElement) {
      const tooltip = userElement.getAttribute('data-tooltip') ||
                     userElement.getAttribute('aria-label');
      const emailMatch = tooltip.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      this.currentUser = emailMatch ? emailMatch[1] : 'unknown';
    } else {
      // Fallback: try to get from page structure
      this.currentUser = this.extractUserFromDOM();
    }

    console.log('Current user:', this.currentUser);
  },

  extractUserFromDOM() {
    // Try various selectors to find user info
    const selectors = [
      '[data-tooltip*="@"]',
      '[aria-label*="@"]',
      '.gb_d .gb_b',
      '.gb_A .gb_z'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        const text = element.textContent || element.getAttribute('data-tooltip') || element.getAttribute('aria-label');
        const emailMatch = text.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
        if (emailMatch) return emailMatch[1];
      }
    }

    return 'unknown_user_' + Date.now();
  }
  

};

// Make it available globally
window.revisionTracker = revisionTracker;

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => revisionTracker.init());
} else {
  revisionTracker.init();
}

} // End of duplicate prevention check

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getStatus') {
    sendResponse({
      user: revisionTracker.currentUser,
      documentId: revisionTracker.documentId,
      hasRevisionData: window.apiClient ? window.apiClient.hasRevisionData() : false
    });
  }

  if (request.action === 'fetchAllRevisionData') {
    if (window.apiClient) {
      window.apiClient.fetchAllRevisionData(true) // Force refresh and get ALL revisions
        .then(data => {
          sendResponse({ success: true, data });
        })
        .catch(error => {
          sendResponse({ success: false, error: error.message });
        });
      return true; // Indicates we will send a response asynchronously
    } else {
      sendResponse({ success: false, error: 'API client not available' });
    }
  }

  if (request.action === 'analyzeRevisions') {
    if (window.apiClient && window.apiClient.hasRevisionData()) {
      try {
        const revisionData = window.apiClient.getCachedRevisionData();
        const analyzer = new RevisionAnalyzer();
        const analysisResults = analyzer.parseRevisionData(revisionData);
        const report = analyzer.generateReport();

        sendResponse({
          success: true,
          report,
          csvData: {
            comprehensive: analyzer.exportComprehensiveCSV(),
            userSummary: analyzer.exportUserSummaryCSV(),
            timeline: analyzer.exportTimelineCSV()
          }
        });
      } catch (error) {
        sendResponse({ success: false, error: error.message });
      }
    } else {
      sendResponse({ success: false, error: 'No revision data available. Please fetch revision data first.' });
    }
  }

  if (request.action === 'getDocumentMetadata') {
    const metadata = window.apiClient ?
      window.apiClient.getDocumentMetadata() :
      {
        id: revisionTracker.documentId,
        title: document.title.replace(' - Google Docs', ''),
        url: window.location.href,
        lastAccessed: new Date().toISOString()
      };
    sendResponse(metadata);
  }
});
