# ecogo.ai Time Tracker

A comprehensive Chrome extension for analyzing revision history on Google Documents. Developed by ecogo.ai - Advanced AI-powered productivity tools for modern teams.

## 🚀 Features

### ⏱️ **Real-Time Activity Tracking**
- Automatically tracks time spent actively working on Google Docs
- Pauses tracking during inactivity (30+ seconds)
- Tracks multiple users and sessions
- Persistent storage across browser sessions

### 📝 **Revision History Analysis**
- Fetches and analyzes Google Docs revision data
- Identifies all contributors and their contributions
- Calculates character counts and revision frequency
- Tracks working sessions and time patterns

### 📊 **Unified Reporting**
- Combines time tracking and revision analysis
- Generates contribution scores based on multiple factors
- Provides payment recommendations
- Exports data in CSV and JSON formats

### 💰 **Payment Calculation**
- Weighted contribution scoring system:
  - 40% based on active time tracking
  - 30% based on number of revisions
  - 30% based on characters/content added
- Detailed payment recommendations
- Export-ready CSV files for accounting

## 📦 Installation

### Method 1: Load as Unpacked Extension (Development)

1. **Download the Extension Files**
   - Clone or download this repository
   - Ensure all files are in a single folder

2. **Open Chrome Extensions Page**
   - Open Chrome browser
   - Navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top-right)

3. **Load the Extension**
   - Click "Load unpacked"
   - Select the folder containing the extension files
   - The extension should appear in your extensions list

4. **Pin the Extension**
   - Click the puzzle piece icon in Chrome toolbar
   - Find "Google Docs Time & Revision Tracker"
   - Click the pin icon to keep it visible

### Method 2: Chrome Web Store (Coming Soon)
The extension will be available on the Chrome Web Store for easy installation.

## 🎯 Usage Guide

### Getting Started

1. **Open a Google Document**
   - Navigate to any Google Docs document
   - The extension automatically starts tracking when you begin working

2. **Access the Extension**
   - Click the extension icon in your Chrome toolbar
   - The popup will show current tracking status

### Using the Interface

#### **Overview Tab**
- **Status Indicator**: Shows if tracking is active (green) or inactive (red)
- **User Information**: Displays current user and document details
- **Time Summary**: Shows time spent by each user
- **Controls**: Refresh data or clear stored information

#### **Revisions Tab**
- **Fetch Revision Data**: Loads revision history from Google Docs
- **Analyze Revisions**: Processes revision data for detailed analysis
- **Revision Summary**: Shows contributor statistics and timeline

#### **Reports Tab**
- **Generate Unified Report**: Combines time tracking and revision analysis
- **Export Options**: Download data in various formats
- **Contribution Rankings**: See who contributed most to the document

#### **Help Tab**
- **Step-by-step instructions** for all features
- **Troubleshooting guide** for common issues
- **Feature explanations** and best practices

### Generating Reports

#### **For Payment Calculation:**

1. **Go to Reports Tab**
   - Click "Generate Unified Report"
   - Wait for analysis to complete

2. **Export Payment Data**
   - Click "Export Unified CSV"
   - File includes contribution scores and recommendations

3. **Review Results**
   - Users ranked by contribution score
   - Payment recommendations provided
   - Detailed breakdown of time, revisions, and content

#### **For Detailed Analysis:**

1. **Export Full JSON Report**
   - Contains complete analysis data
   - Includes timeline, sessions, and metadata
   - Suitable for technical analysis

## 📋 Understanding the Data

### **Time Tracking Data**
- **Active Time**: Only counts time when actively working
- **Sessions**: Grouped periods of activity
- **Inactivity Threshold**: 30 seconds before pausing

### **Revision Analysis**
- **Revisions**: Individual document changes
- **Characters**: Content added/modified
- **Working Sessions**: Grouped revisions within 30 minutes

### **Contribution Scoring**
The unified scoring system considers:
- **Time Spent (40%)**: Active working time
- **Revisions (30%)**: Number of document changes
- **Content (30%)**: Characters and content added

### **Payment Recommendations**
- **80-100%**: Primary contributor - Full payment
- **50-79%**: Significant contributor - Substantial payment
- **20-49%**: Moderate contributor - Partial payment
- **0-19%**: Minor contributor - Minimal payment

## 🔧 Troubleshooting

### **Extension Not Working**
- Refresh the Google Docs page
- Ensure you're on a document (not spreadsheet/slides)
- Check that extension is enabled in Chrome
- Try disabling and re-enabling the extension

### **No Revision Data**
- Some documents may have restricted revision access
- Try refreshing and fetching again
- Time tracking will still work independently
- Check if you have edit access to the document

### **Inaccurate Time Tracking**
- Extension only tracks active time (mouse/keyboard activity)
- Pauses automatically during inactivity
- Multiple tabs with Google Docs may affect accuracy
- Close unused document tabs for best results

### **Export Issues**
- Ensure popup blockers are disabled
- Try different export formats (CSV vs JSON)
- Check browser download settings
- Generate report before exporting

## 🛠️ Technical Details

### **Files Structure**
```
├── manifest.json          # Extension configuration
├── background.js          # Background service worker
├── content.js            # Main content script
├── popup.html            # Extension popup interface
├── popup.js              # Popup functionality
├── popup.css             # Popup styling
├── api-client.js         # Google Docs API integration
├── revision-analyzer.js  # Revision analysis engine
├── unified-reporter.js   # Combined reporting system
└── README.md             # This documentation
```

### **Data Storage**
- **Local Storage**: All data stored locally in browser
- **No External Servers**: Complete privacy protection
- **Automatic Cleanup**: Old entries removed after 30 days
- **Export Options**: Full data portability

### **Privacy & Security**
- **No Data Collection**: Extension doesn't send data anywhere
- **Local Processing**: All analysis done in browser
- **Google Account Integration**: Uses existing Google authentication
- **Minimal Permissions**: Only requests necessary access

## 🤝 Contributing

We welcome contributions! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support & Contact

### **📧 Email Support**
For technical support, questions, or business inquiries:
**<EMAIL>**

### **🌐 Website**
Visit our website for more information:
**www.ecogo.ai**

### **💡 About ecogo.ai**
ecogo.ai develops advanced AI-powered productivity tools for modern teams and businesses. Our solutions help organizations optimize workflows, analyze data, and improve collaboration efficiency.

### **🔧 Technical Support**
For immediate help:
1. Check the troubleshooting section above
2. Review the help tab in the extension
3. Contact <NAME_EMAIL>

---

**ecogo.ai Time Tracker v2.0** - Comprehensive revision analysis for Google Documents
**Compatible with**: Chrome 88+ and all Google Docs documents
**Developed by**: ecogo.ai | **Support**: <EMAIL> | **Web**: www.ecogo.ai
