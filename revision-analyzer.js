// Comprehensive Revision Analyzer for Google Docs API responses
// Prevent duplicate class definition
if (typeof window.RevisionAnalyzer === 'undefined') {

class RevisionAnalyzer {
  constructor() {
    this.revisionData = null;
    this.userMap = null;
    this.analysisResults = null;
  }

  /**
   * Parse the Google Docs revision API response
   * @param {Object} apiResponse - The raw API response from Google Docs
   */
  parseRevisionData(apiResponse) {
    try {
      // Handle both string and object inputs
      const data = typeof apiResponse === 'string' ? JSON.parse(apiResponse) : apiResponse;
      
      this.revisionData = data.tileInfo || [];
      this.userMap = data.userMap || {};
      
      console.log('Parsed revision data:', {
        revisions: this.revisionData.length,
        users: Object.keys(this.userMap).length
      });
      
      return this.analyzeRevisions();
    } catch (error) {
      console.error('Error parsing revision data:', error);
      throw new Error('Invalid revision data format');
    }
  }

  /**
   * Analyze revisions and calculate user contributions
   */
  analyzeRevisions() {
    if (!this.revisionData || !this.userMap) {
      throw new Error('No revision data to analyze');
    }

    const userContributions = {};
    const timeline = [];
    let totalUserContributions = 0; // Count total user contributions, not just revisions
    let totalTimeSpan = 0;

    // Initialize user contributions
    Object.keys(this.userMap).forEach(userId => {
      userContributions[userId] = {
        name: this.userMap[userId].name,
        photo: this.userMap[userId].photo,
        color: this.userMap[userId].color,
        revisions: [],
        totalRevisions: 0,
        totalCharacters: 0,
        timeSpent: 0,
        firstActivity: null,
        lastActivity: null,
        workingSessions: []
      };
    });

    // Process each revision
    this.revisionData.forEach((revision, index) => {
      const startTime = new Date(revision.endMillis);
      const characters = revision.end - revision.start + 1;
      
      // Process each user in this revision
      revision.users.forEach(userId => {
        if (userContributions[userId]) {
          const contribution = {
            revisionIndex: index,
            startPos: revision.start,
            endPos: revision.end,
            characters: characters,
            timestamp: startTime,
            name: revision.name || null,
            expandable: revision.expandable
          };

          userContributions[userId].revisions.push(contribution);
          userContributions[userId].totalRevisions++;
          userContributions[userId].totalCharacters += characters;

          // Increment total user contributions counter
          totalUserContributions++;

          // Update first/last activity
          if (!userContributions[userId].firstActivity || startTime < userContributions[userId].firstActivity) {
            userContributions[userId].firstActivity = startTime;
          }
          if (!userContributions[userId].lastActivity || startTime > userContributions[userId].lastActivity) {
            userContributions[userId].lastActivity = startTime;
          }
        }
      });

      // Add to timeline
      timeline.push({
        timestamp: startTime,
        users: revision.users.map(userId => ({
          id: userId,
          name: this.userMap[userId]?.name || 'Unknown'
        })),
        characters: characters,
        position: `${revision.start}-${revision.end}`,
        name: revision.name
      });
    });

    // Calculate time spent and working sessions for each user
    Object.keys(userContributions).forEach(userId => {
      const user = userContributions[userId];
      if (user.revisions.length > 0) {
        // Sort revisions by timestamp
        user.revisions.sort((a, b) => a.timestamp - b.timestamp);
        
        // Calculate working sessions (group revisions within 30 minutes)
        const sessionGap = 30 * 60 * 1000; // 30 minutes in milliseconds
        let currentSession = {
          start: user.revisions[0].timestamp,
          end: user.revisions[0].timestamp,
          revisions: 1,
          characters: user.revisions[0].characters
        };
        
        for (let i = 1; i < user.revisions.length; i++) {
          const revision = user.revisions[i];
          const timeDiff = revision.timestamp - currentSession.end;
          
          if (timeDiff <= sessionGap) {
            // Extend current session
            currentSession.end = revision.timestamp;
            currentSession.revisions++;
            currentSession.characters += revision.characters;
          } else {
            // Start new session
            user.workingSessions.push(currentSession);
            currentSession = {
              start: revision.timestamp,
              end: revision.timestamp,
              revisions: 1,
              characters: revision.characters
            };
          }
        }
        
        // Add the last session
        user.workingSessions.push(currentSession);
        
        // Calculate total time spent (sum of all sessions)
        user.timeSpent = user.workingSessions.reduce((total, session) => {
          return total + (session.end - session.start);
        }, 0);
      }
    });

    // Calculate total time span of the document
    if (timeline.length > 0) {
      const sortedTimeline = timeline.sort((a, b) => a.timestamp - b.timestamp);
      totalTimeSpan = sortedTimeline[sortedTimeline.length - 1].timestamp - sortedTimeline[0].timestamp;
    }

    this.analysisResults = {
      userContributions,
      timeline: timeline.sort((a, b) => a.timestamp - b.timestamp),
      summary: {
        totalRevisions: this.revisionData.length, // Number of revision objects
        totalUserContributions, // Total user contributions across all revisions
        totalUsers: Object.keys(this.userMap).length,
        totalTimeSpan,
        documentPeriod: {
          start: timeline.length > 0 ? Math.min(...timeline.map(t => t.timestamp)) : null,
          end: timeline.length > 0 ? Math.max(...timeline.map(t => t.timestamp)) : null
        }
      }
    };

    return this.analysisResults;
  }

  /**
   * Generate a detailed report
   */
  generateReport() {
    if (!this.analysisResults) {
      throw new Error('No analysis results available. Run parseRevisionData first.');
    }

    const { userContributions, timeline, summary } = this.analysisResults;
    
    // Sort users by total contribution
    const sortedUsers = Object.entries(userContributions)
      .filter(([_, user]) => user.totalRevisions > 0)
      .sort((a, b) => b[1].totalRevisions - a[1].totalRevisions);

    const report = {
      summary: {
        totalRevisions: summary.totalRevisions,
        totalUserContributions: summary.totalUserContributions,
        totalUsers: sortedUsers.length,
        documentPeriod: {
          start: summary.documentPeriod.start ? new Date(summary.documentPeriod.start).toLocaleString() : 'N/A',
          end: summary.documentPeriod.end ? new Date(summary.documentPeriod.end).toLocaleString() : 'N/A',
          duration: this.formatDuration(summary.totalTimeSpan)
        }
      },
      userDetails: sortedUsers.map(([userId, user]) => ({
        id: userId,
        name: user.name,
        totalRevisions: user.totalRevisions,
        totalCharacters: user.totalCharacters,
        contributionPercentage: ((user.totalRevisions / summary.totalUserContributions) * 100).toFixed(1),
        timeSpent: this.formatDuration(user.timeSpent),
        timeSpentMs: user.timeSpent,
        workingSessions: user.workingSessions.length,
        firstActivity: user.firstActivity ? user.firstActivity.toLocaleString() : 'N/A',
        lastActivity: user.lastActivity ? user.lastActivity.toLocaleString() : 'N/A',
        averageSessionDuration: user.workingSessions.length > 0 ?
          this.formatDuration(user.timeSpent / user.workingSessions.length) : '0m',
        color: user.color
      })),
      timeline: timeline.map(event => ({
        timestamp: event.timestamp.toLocaleString(),
        users: event.users.map(u => u.name).join(', '),
        characters: event.characters,
        position: event.position,
        name: event.name || 'Unnamed revision'
      }))
    };

    return report;
  }

  /**
   * Format duration in milliseconds to human readable format
   */
  formatDuration(ms) {
    if (!ms || ms < 0) return '0m';
    
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Export comprehensive revision data as CSV
   */
  exportComprehensiveCSV() {
    if (!this.analysisResults) {
      throw new Error('No analysis results available');
    }

    // Create detailed CSV with all revision information
    let csvContent = 'Revision #,Date,Time,User Name,Start Position,End Position,Characters,Content Length,Revision Name,Expandable,Revision ID\n';

    // Sort revisions by date
    const sortedRevisions = [...this.revisionData].sort((a, b) => a.endMillis - b.endMillis);

    sortedRevisions.forEach((revision, index) => {
      const date = new Date(revision.endMillis);
      const dateStr = date.toLocaleDateString();
      const timeStr = date.toLocaleTimeString();
      const characters = revision.end - revision.start + 1;
      const revisionName = revision.name || 'Unnamed';

      // Handle multiple users in one revision
      revision.users.forEach(userId => {
        const userName = this.userMap[userId]?.name || 'Unknown User';

        csvContent += `${index + 1},"${dateStr}","${timeStr}","${userName}",${revision.start},${revision.end},${characters},${revision.end - revision.start + 1},"${revisionName}",${revision.expandable},"${revision.revisionMac}"\n`;
      });
    });

    return csvContent;
  }

  /**
   * Export user summary as CSV
   */
  exportUserSummaryCSV() {
    if (!this.analysisResults) {
      throw new Error('No analysis results available');
    }

    const report = this.generateReport();
    let csvContent = 'User Name,User Contributions,Characters Added,Contribution %,Working Sessions,First Activity,Last Activity,User Color\n';

    report.userDetails.forEach(user => {
      csvContent += `"${user.name}",${user.totalRevisions},${user.totalCharacters},${user.contributionPercentage}%,${user.workingSessions},"${user.firstActivity}","${user.lastActivity}","${user.color}"\n`;
    });

    return csvContent;
  }

  /**
   * Export timeline as CSV
   */
  exportTimelineCSV() {
    if (!this.analysisResults) {
      throw new Error('No analysis results available');
    }

    let csvContent = 'Date,Time,Users,Characters,Position Range,Revision Name\n';

    this.analysisResults.timeline.forEach(event => {
      const date = new Date(event.timestamp);
      const dateStr = date.toLocaleDateString();
      const timeStr = date.toLocaleTimeString();
      const users = event.users.map(u => u.name).join('; ');

      csvContent += `"${dateStr}","${timeStr}","${users}",${event.characters},"${event.position}","${event.name || 'Unnamed'}"\n`;
    });

    return csvContent;
  }
}

// Make it available globally
window.RevisionAnalyzer = RevisionAnalyzer;

} // End of duplicate prevention check
