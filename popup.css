/* Enhanced popup styles for Google Docs Tracker */

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  width: 450px;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  margin: 0;
  background: #f8f9fa;
}

/* Header */
.header {
  background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
  color: white;
  padding: 12px 15px;
  text-align: center;
  margin: 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.header .logo {
  height: 24px;
  width: auto;
  filter: brightness(0) invert(1); /* Makes the external logo white */
  transition: opacity 0.3s ease;
}

.header .logo:not([src]) {
  display: none;
}

/* Fallback if logo fails to load */
.header .logo[alt]:after {
  content: attr(alt);
  font-weight: bold;
  color: white;
}

.header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Tab navigation */
.tabs {
  display: flex;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.tab {
  flex: 1;
  padding: 12px 8px;
  text-align: center;
  cursor: pointer;
  border: none;
  background: none;
  font-size: 13px;
  color: #5f6368;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
  font-weight: 500;
}

.tab:hover {
  background: #f8f9fa;
}

/* Tab content */
.tab-content {
  display: none;
  padding: 15px;
  background: white;
  min-height: 300px;
}

.tab-content.active {
  display: block;
}

/* Status indicator */
.status {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 6px;
  background: #f5f5f5;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 10px;
}

.active-indicator { 
  background-color: #34a853; 
  box-shadow: 0 0 0 2px rgba(52, 168, 83, 0.2);
}

.inactive-indicator { 
  background-color: #ea4335; 
  box-shadow: 0 0 0 2px rgba(234, 67, 53, 0.2);
}

/* User info */
.user-info {
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 13px;
  border-left: 3px solid #1a73e8;
}

/* Controls */
.controls {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

/* Buttons */
button {
  padding: 8px 12px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  flex: 1;
  min-width: 100px;
  transition: all 0.2s ease;
}

button:hover {
  background: #f8f9fa;
  border-color: #1a73e8;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.primary-btn {
  background: #1a73e8;
  color: white;
  border-color: #1a73e8;
}

.primary-btn:hover {
  background: #1557b0;
}

.success-btn {
  background: #34a853;
  color: white;
  border-color: #34a853;
}

.success-btn:hover {
  background: #2d8f47;
}

/* Time summary */
.time-summary {
  margin-bottom: 20px;
}

.time-entry {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
}

.time-entry:last-child {
  border-bottom: none;
}

.user-name {
  font-weight: 500;
  color: #1a73e8;
}

.time-duration {
  color: #5f6368;
  font-family: monospace;
}

.total-time {
  font-size: 14px;
  font-weight: 600;
  color: #1a73e8;
  text-align: center;
  margin: 15px 0;
  padding: 8px;
  background: #e8f0fe;
  border-radius: 6px;
}

/* Revision analysis */
.revision-summary {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 13px;
  border-left: 3px solid #34a853;
}

.revision-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
}

.revision-user:last-child {
  border-bottom: none;
}

.user-contribution {
  font-size: 12px;
  color: #5f6368;
  margin-top: 2px;
}

/* Export section */
.export-section {
  border-top: 1px solid #e0e0e0;
  padding-top: 15px;
  margin-top: 15px;
}

/* Loading states */
.loading {
  text-align: center;
  color: #5f6368;
  padding: 20px;
  font-style: italic;
}

/* Help section */
.help-section {
  max-height: 400px;
  overflow-y: auto;
}

.help-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.help-item:last-child {
  border-bottom: none;
}

.help-title {
  font-weight: 600;
  color: #1a73e8;
  margin-bottom: 8px;
  font-size: 14px;
}

.help-content {
  font-size: 13px;
  line-height: 1.4;
  color: #5f6368;
}

.help-steps {
  list-style: none;
  padding: 0;
  margin: 8px 0;
  counter-reset: step-counter;
}

.help-steps li {
  padding: 4px 0;
  padding-left: 20px;
  position: relative;
  margin-bottom: 4px;
}

.help-steps li:before {
  content: counter(step-counter);
  counter-increment: step-counter;
  position: absolute;
  left: 0;
  top: 4px;
  background: #1a73e8;
  color: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* Scrollbar styling */
.help-section::-webkit-scrollbar {
  width: 6px;
}

.help-section::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.help-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.help-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  body {
    width: 350px;
  }
  
  .controls {
    flex-direction: column;
  }
  
  button {
    min-width: auto;
  }
}

/* Animation for status changes */
.status-indicator {
  transition: all 0.3s ease;
}

/* Footer */
.footer {
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  margin: 0;
}

.footer a:hover {
  text-decoration: underline !important;
}

/* Hover effects for interactive elements */
.revision-user:hover {
  background: #f8f9fa;
  border-radius: 4px;
  margin: 0 -4px;
  padding: 8px 4px;
}

.time-entry:hover {
  background: #f8f9fa;
  border-radius: 4px;
  margin: 0 -4px;
  padding: 8px 4px;
}
