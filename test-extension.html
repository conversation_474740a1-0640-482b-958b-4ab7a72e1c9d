<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #1a73e8;
        }
        .success {
            border-left-color: #34a853;
            background: #e8f5e8;
        }
        .warning {
            border-left-color: #fbbc04;
            background: #fef7e0;
        }
        .error {
            border-left-color: #ea4335;
            background: #fce8e6;
        }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1557b0;
        }
        .code {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }
        .step-number {
            background: #1a73e8;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div style="text-align: center; margin-bottom: 30px;">
        <img src="icon.png" alt="ecogo.ai" style="height: 40px; margin-bottom: 10px;">
        <h1>🧪 ecogo.ai Time Tracker - Test Page</h1>
    </div>
    
    <div class="test-section">
        <h2>📋 Pre-Installation Checklist</h2>
        <div class="step">
            <span class="step-number">1</span>
            <strong>Chrome Browser:</strong> Ensure you're using Chrome 88 or later
        </div>
        <div class="step">
            <span class="step-number">2</span>
            <strong>Developer Mode:</strong> Enable Developer mode in chrome://extensions/
        </div>
        <div class="step">
            <span class="step-number">3</span>
            <strong>Extension Files:</strong> All files should be in the same directory
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 Installation Steps</h2>
        <div class="step">
            <span class="step-number">1</span>
            <strong>Open Extensions Page:</strong>
            <div class="code">chrome://extensions/</div>
        </div>
        <div class="step">
            <span class="step-number">2</span>
            <strong>Enable Developer Mode:</strong> Toggle the switch in the top-right corner
        </div>
        <div class="step">
            <span class="step-number">3</span>
            <strong>Load Extension:</strong> Click "Load unpacked" and select the extension folder
        </div>
        <div class="step">
            <span class="step-number">4</span>
            <strong>Pin Extension:</strong> Click the puzzle piece icon and pin the extension
        </div>
    </div>

    <div class="test-section">
        <h2>✅ Testing Checklist</h2>
        
        <h3>Basic Functionality Tests</h3>
        <div class="step">
            <input type="checkbox" id="test1"> 
            <label for="test1">Extension loads without errors</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test2"> 
            <label for="test2">Popup opens when clicking extension icon</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test3"> 
            <label for="test3">All four tabs (Overview, Revisions, Reports, Help) are visible</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test4"> 
            <label for="test4">Tab switching works correctly</label>
        </div>

        <h3>Google Docs Integration Tests</h3>
        <div class="step">
            <input type="checkbox" id="test5"> 
            <label for="test5">Extension activates on Google Docs pages</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test6"> 
            <label for="test6">Time tracking starts automatically</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test7"> 
            <label for="test7">Document metadata is detected correctly</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test8"> 
            <label for="test8">User information is captured</label>
        </div>

        <h3>Revision Analysis Tests</h3>
        <div class="step">
            <input type="checkbox" id="test9"> 
            <label for="test9">"Fetch Revision Data" button works</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test10"> 
            <label for="test10">Revision data is displayed correctly</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test11"> 
            <label for="test11">"Analyze Revisions" processes data successfully</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test12"> 
            <label for="test12">User contributions are calculated accurately</label>
        </div>

        <h3>Unified Reporting Tests</h3>
        <div class="step">
            <input type="checkbox" id="test13"> 
            <label for="test13">"Generate Unified Report" creates comprehensive analysis</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test14"> 
            <label for="test14">Contribution scores are calculated correctly</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test15"> 
            <label for="test15">Payment recommendations are provided</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test16"> 
            <label for="test16">CSV export works and contains expected data</label>
        </div>
        <div class="step">
            <input type="checkbox" id="test17"> 
            <label for="test17">JSON export works and is properly formatted</label>
        </div>
    </div>

    <div class="test-section">
        <h2>🚀 Quick Test Procedure</h2>
        <div class="step">
            <span class="step-number">1</span>
            <strong>Install Extension:</strong> Follow installation steps above
        </div>
        <div class="step">
            <span class="step-number">2</span>
            <strong>Open Google Docs:</strong> 
            <button onclick="window.open('https://docs.google.com/document/create', '_blank')">
                Create New Document
            </button>
        </div>
        <div class="step">
            <span class="step-number">3</span>
            <strong>Test Time Tracking:</strong> Type some content and check if tracking is active
        </div>
        <div class="step">
            <span class="step-number">4</span>
            <strong>Test Revision Analysis:</strong> Go to Revisions tab and fetch data
        </div>
        <div class="step">
            <span class="step-number">5</span>
            <strong>Test Unified Reports:</strong> Generate and export a comprehensive report
        </div>
    </div>

    <div class="test-section warning">
        <h2>⚠️ Common Issues & Solutions</h2>
        <div class="step">
            <strong>Extension not loading:</strong> Check that all files are in the same folder and manifest.json is present
        </div>
        <div class="step">
            <strong>Popup not opening:</strong> Refresh the Google Docs page and try again
        </div>
        <div class="step">
            <strong>No revision data:</strong> Some documents may have restricted access - try with a document you own
        </div>
        <div class="step">
            <strong>Export not working:</strong> Check popup blockers and browser download settings
        </div>
    </div>

    <div class="test-section success">
        <h2>🎉 Success Indicators</h2>
        <div class="step">
            ✅ Extension icon appears in Chrome toolbar
        </div>
        <div class="step">
            ✅ Popup opens with four functional tabs
        </div>
        <div class="step">
            ✅ Time tracking shows active status on Google Docs
        </div>
        <div class="step">
            ✅ Revision data can be fetched and analyzed
        </div>
        <div class="step">
            ✅ Unified reports generate with contribution scores
        </div>
        <div class="step">
            ✅ CSV exports contain payment calculation data
        </div>
    </div>

    <div class="test-section">
        <h2>📞 Support & Contact</h2>
        <p><strong>Need help with ecogo.ai Time Tracker?</strong></p>
        <ul>
            <li><strong>📧 Email Support:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
            <li><strong>🌐 Website:</strong> <a href="https://www.ecogo.ai" target="_blank">www.ecogo.ai</a></li>
            <li>Check the browser console for error messages</li>
            <li>Verify all extension files are present</li>
            <li>Try refreshing the Google Docs page</li>
            <li>Check the Help tab in the extension popup</li>
        </ul>

        <p><strong>About ecogo.ai:</strong> Advanced AI-powered productivity tools for modern teams and businesses.</p>
    </div>

    <script>
        // Simple test progress tracking
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        const totalTests = checkboxes.length;
        
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateProgress);
        });
        
        function updateProgress() {
            const completed = document.querySelectorAll('input[type="checkbox"]:checked').length;
            const percentage = Math.round((completed / totalTests) * 100);
            
            console.log(`Test Progress: ${completed}/${totalTests} (${percentage}%)`);
            
            if (completed === totalTests) {
                alert('🎉 All tests completed! Your extension should be working perfectly.');
            }
        }
        
        // Add some helpful console messages
        console.log('🧪 Extension Test Page Loaded');
        console.log(`📋 Total Tests: ${totalTests}`);
        console.log('💡 Tip: Open browser console to see test progress');
    </script>
</body>
</html>
