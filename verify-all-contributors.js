// Comprehensive verification script for all contributors
// This script verifies that we capture ALL contributors mentioned in the version history

console.log('🔍 ===== COMPREHENSIVE CONTRIBUTOR VERIFICATION =====');

// Expected contributors based on the version history provided
const expectedContributors = [
  'KSWMP DPMU Thrissur',
  'MEE DPMU TSR KSWMP', 
  'PIU CHAVAKKAD',
  'Sunil N',
  'SCS Menon',
  'RESITHA K G',
  'ajith krishna',
  'Sajil K',
  'Jayanesh Mk',
  'All anonymous users' // This appears as "Unknown User" in API
];

// Expected May 28, 2025 revisions
const expectedMay28Revisions = [
  { time: '16:11', user: 'KSWMP DPMU Thrissur' },
  { time: '18:45', user: 'KSWMP DPMU Thrissur' },
  { time: '20:15', user: 'KSWMP DPMU Thrissur' }
];

async function verifyAllContributors() {
  console.log('🎯 Target: Verify all contributors from version history are captured');
  console.log(`📋 Expected contributors: ${expectedContributors.length}`);
  console.log(`   ${expectedContributors.join(', ')}`);
  
  try {
    // Wait for API client
    if (!window.apiClient) {
      console.error('❌ API client not found. Make sure extension is loaded.');
      return;
    }

    // Force a fresh fetch with enhanced coverage
    console.log('\n🔄 Fetching revision data with enhanced coverage...');
    const revisionData = await window.apiClient.fetchAllRevisionData(true);
    
    if (!revisionData || !revisionData.tileInfo) {
      console.error('❌ No revision data returned');
      return;
    }

    console.log(`✅ Retrieved ${revisionData.tileInfo.length} total revisions`);

    // Analyze all users found
    const allUsers = revisionData.userMap || {};
    const userNames = Object.values(allUsers).map(u => u.name || 'Unknown User');
    const uniqueUserNames = [...new Set(userNames)];
    
    console.log(`\n👥 All users found (${uniqueUserNames.length} unique):`);
    uniqueUserNames.forEach((name, idx) => {
      console.log(`   ${idx + 1}. ${name}`);
    });

    // Check each expected contributor
    console.log(`\n🔍 Checking each expected contributor:`);
    const foundContributors = [];
    const missingContributors = [];
    
    expectedContributors.forEach(expectedName => {
      // Handle special case for anonymous users
      const isFound = expectedName === 'All anonymous users' 
        ? userNames.includes('Unknown User') || userNames.includes('Anonymous')
        : userNames.includes(expectedName);
        
      if (isFound) {
        foundContributors.push(expectedName);
        console.log(`   ✅ ${expectedName}`);
      } else {
        missingContributors.push(expectedName);
        console.log(`   ❌ ${expectedName} - NOT FOUND`);
      }
    });

    // Verify May 28, 2025 data
    console.log(`\n📅 Verifying May 28, 2025 data:`);
    const may28Revisions = revisionData.tileInfo.filter(r => {
      const date = new Date(r.endMillis);
      return date.getFullYear() === 2025 && date.getMonth() === 4 && date.getDate() === 28;
    });

    console.log(`   Found ${may28Revisions.length} May 28, 2025 revisions`);
    may28Revisions.forEach((rev, idx) => {
      const date = new Date(rev.endMillis);
      const users = rev.users.map(uid => allUsers[uid]?.name || 'Unknown').join(', ');
      console.log(`   ${idx + 1}. ${date.toLocaleTimeString()} - ${users}`);
    });

    // Check date coverage
    console.log(`\n📊 Date coverage analysis:`);
    if (revisionData.tileInfo.length > 0) {
      const dates = revisionData.tileInfo.map(r => new Date(r.endMillis)).sort((a, b) => a - b);
      const earliestDate = dates[0];
      const latestDate = dates[dates.length - 1];
      
      console.log(`   Earliest: ${earliestDate.toLocaleString()}`);
      console.log(`   Latest: ${latestDate.toLocaleString()}`);
      console.log(`   Total span: ${Math.ceil((latestDate - earliestDate) / (1000 * 60 * 60 * 24))} days`);
    }

    // Summary
    console.log(`\n🎯 ===== VERIFICATION SUMMARY =====`);
    console.log(`📊 Total revisions: ${revisionData.tileInfo.length}`);
    console.log(`👥 Contributors found: ${foundContributors.length}/${expectedContributors.length}`);
    console.log(`📅 May 28, 2025 revisions: ${may28Revisions.length}`);
    
    if (foundContributors.length === expectedContributors.length) {
      console.log(`🎉 SUCCESS: All expected contributors found!`);
    } else {
      console.log(`⚠️ INCOMPLETE: Missing ${missingContributors.length} contributors:`);
      missingContributors.forEach(name => console.log(`   - ${name}`));
    }

    // Detailed analysis for missing contributors
    if (missingContributors.length > 0) {
      console.log(`\n🔍 Analysis for missing contributors:`);
      console.log(`💡 Possible reasons:`);
      console.log(`   1. Contributors may be in revisions not captured by current API ranges`);
      console.log(`   2. Contributors may appear under different names in API vs UI`);
      console.log(`   3. Some revisions may require different API parameters`);
      console.log(`   4. Contributors may be in collaborative editing sessions not captured as individual revisions`);
      
      console.log(`\n🔧 Recommendations:`);
      console.log(`   1. Try refreshing and running the test again`);
      console.log(`   2. Check if missing contributors appear in the browser's version history`);
      console.log(`   3. Verify the document has the expected revision history`);
    }

    return {
      success: foundContributors.length === expectedContributors.length,
      totalRevisions: revisionData.tileInfo.length,
      foundContributors: foundContributors.length,
      expectedContributors: expectedContributors.length,
      missingContributors: missingContributors,
      may28Revisions: may28Revisions.length
    };

  } catch (error) {
    console.error('💥 Verification failed:', error);
    return { success: false, error: error.message };
  }
}

// Quick check function for current cached data
function quickContributorCheck() {
  console.log('⚡ Quick contributor check on cached data...');
  
  if (!window.apiClient) {
    console.log('❌ API client not available');
    return;
  }
  
  const cachedData = window.apiClient.getCachedRevisionData();
  if (!cachedData) {
    console.log('❌ No cached data. Run fetchAllRevisionData first.');
    return;
  }
  
  const userNames = Object.values(cachedData.userMap || {}).map(u => u.name || 'Unknown User');
  const uniqueUserNames = [...new Set(userNames)];
  
  console.log(`👥 Currently cached contributors (${uniqueUserNames.length}):`);
  uniqueUserNames.forEach(name => console.log(`   - ${name}`));
  
  const missing = expectedContributors.filter(expected => {
    return expected === 'All anonymous users' 
      ? !userNames.includes('Unknown User') && !userNames.includes('Anonymous')
      : !userNames.includes(expected);
  });
  
  if (missing.length === 0) {
    console.log('🎉 All expected contributors found in cache!');
  } else {
    console.log(`⚠️ Missing from cache: ${missing.join(', ')}`);
  }
}

// Make functions available globally
window.verifyAllContributors = verifyAllContributors;
window.quickContributorCheck = quickContributorCheck;

console.log('📝 Verification script loaded. Run verifyAllContributors() to test.');
