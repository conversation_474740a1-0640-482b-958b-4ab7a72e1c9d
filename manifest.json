{"manifest_version": 3, "name": "ecogo.ai Time Tracker", "version": "2.0", "description": "Comprehensive revision analysis and time tracking for Google Documents by ecogo.ai", "permissions": ["activeTab", "storage", "tabs", "scripting"], "host_permissions": ["https://docs.google.com/*", "https://*.googleapis.com/*"], "content_scripts": [{"matches": ["https://docs.google.com/document/*"], "js": ["revision-analyzer.js", "api-client.js", "content.js"], "run_at": "document_idle"}], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "ecogo.ai Time Tracker", "default_icon": {"16": "icon.png", "32": "icon.png", "48": "icon.png", "128": "icon.png"}}, "icons": {"16": "icon.png", "32": "icon.png", "48": "icon.png", "128": "icon.png"}, "web_accessible_resources": [{"resources": ["injected.js", "help.html", "revision-analyzer.js"], "matches": ["https://docs.google.com/*"]}]}