// Unified Reporter - Combines time tracking and revision analysis
class UnifiedReporter {
  constructor() {
    this.timeTrackingData = [];
    this.revisionData = null;
    this.documentMetadata = null;
    this.analyzer = new RevisionAnalyzer();
  }

  /**
   * Load all data for the current document
   */
  async loadData(documentId) {
    try {
      // Load time tracking data
      this.timeTrackingData = await this.loadTimeTrackingData(documentId);
      
      // Load revision data
      this.revisionData = await this.loadRevisionData();
      
      // Load document metadata
      this.documentMetadata = this.getDocumentMetadata();
      
      console.log('Unified data loaded:', {
        timeEntries: this.timeTrackingData.length,
        revisions: this.revisionData?.tileInfo?.length || 0,
        document: this.documentMetadata?.title
      });

      return this.generateUnifiedReport();

    } catch (error) {
      console.error('Error loading unified data:', error);
      throw error;
    }
  }

  /**
   * Load time tracking data from storage
   */
  async loadTimeTrackingData(documentId) {
    return new Promise((resolve) => {
      chrome.storage.local.get(['timeEntries'], (result) => {
        const allEntries = result.timeEntries || [];
        // Filter entries for current document
        const documentEntries = documentId ? 
          allEntries.filter(entry => entry.documentId === documentId) : 
          allEntries;
        resolve(documentEntries);
      });
    });
  }

  /**
   * Load revision data using API client
   */
  async loadRevisionData() {
    if (window.apiClient) {
      try {
        return await window.apiClient.fetchRevisionData();
      } catch (error) {
        console.warn('Could not fetch revision data:', error);
        return null;
      }
    }
    return null;
  }

  /**
   * Get document metadata
   */
  getDocumentMetadata() {
    if (window.apiClient) {
      return window.apiClient.getDocumentMetadata();
    }
    
    // Fallback metadata
    return {
      id: this.extractDocumentId(),
      title: document.title.replace(' - Google Docs', ''),
      url: window.location.href,
      lastAccessed: new Date().toISOString()
    };
  }

  /**
   * Extract document ID from URL
   */
  extractDocumentId() {
    const url = window.location.href;
    const match = url.match(/\/document\/d\/([a-zA-Z0-9-_]+)/);
    return match ? match[1] : null;
  }

  /**
   * Generate unified report combining time tracking and revision analysis
   */
  generateUnifiedReport() {
    const report = {
      metadata: this.documentMetadata,
      summary: {
        reportGenerated: new Date().toISOString(),
        timeTrackingEntries: this.timeTrackingData.length,
        revisionCount: this.revisionData?.tileInfo?.length || 0,
        hasRevisionData: !!this.revisionData
      },
      timeTracking: this.analyzeTimeTracking(),
      revisionAnalysis: this.analyzeRevisions(),
      unified: this.createUnifiedAnalysis()
    };

    return report;
  }

  /**
   * Analyze time tracking data
   */
  analyzeTimeTracking() {
    if (this.timeTrackingData.length === 0) {
      return {
        totalTime: 0,
        users: {},
        sessions: [],
        summary: 'No time tracking data available'
      };
    }

    const userSummary = {};
    let totalTime = 0;
    const sessions = [];

    this.timeTrackingData.forEach(entry => {
      if (!userSummary[entry.user]) {
        userSummary[entry.user] = {
          totalTime: 0,
          sessions: 0,
          firstSession: entry.timestamp,
          lastSession: entry.timestamp
        };
      }

      userSummary[entry.user].totalTime += entry.duration;
      userSummary[entry.user].sessions += 1;
      totalTime += entry.duration;

      if (entry.timestamp < userSummary[entry.user].firstSession) {
        userSummary[entry.user].firstSession = entry.timestamp;
      }
      if (entry.timestamp > userSummary[entry.user].lastSession) {
        userSummary[entry.user].lastSession = entry.timestamp;
      }

      sessions.push({
        user: entry.user,
        duration: entry.duration,
        timestamp: entry.timestamp,
        startTime: new Date(entry.startTime).toISOString(),
        endTime: new Date(entry.endTime).toISOString()
      });
    });

    return {
      totalTime,
      users: userSummary,
      sessions: sessions.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp)),
      summary: `${Object.keys(userSummary).length} users tracked across ${sessions.length} sessions`
    };
  }

  /**
   * Analyze revision data
   */
  analyzeRevisions() {
    if (!this.revisionData) {
      return {
        available: false,
        summary: 'No revision data available'
      };
    }

    try {
      const analysisResults = this.analyzer.parseRevisionData(this.revisionData);
      const report = this.analyzer.generateReport();
      
      return {
        available: true,
        analysisResults,
        report,
        summary: `${report.summary.totalRevisions} revisions by ${report.summary.totalUsers} users`
      };
    } catch (error) {
      console.error('Error analyzing revisions:', error);
      return {
        available: false,
        error: error.message,
        summary: 'Error analyzing revision data'
      };
    }
  }

  /**
   * Create unified analysis combining both data sources
   */
  createUnifiedAnalysis() {
    const timeUsers = this.timeTracking?.users || {};
    const revisionUsers = this.revisionAnalysis?.report?.userDetails || [];
    
    // Create a unified user map
    const unifiedUsers = {};
    
    // Add time tracking users
    Object.entries(timeUsers).forEach(([user, data]) => {
      unifiedUsers[user] = {
        name: user,
        timeTracked: data.totalTime,
        timeSessions: data.sessions,
        revisions: 0,
        characters: 0,
        contributionScore: 0
      };
    });

    // Add revision users
    revisionUsers.forEach(user => {
      if (!unifiedUsers[user.name]) {
        unifiedUsers[user.name] = {
          name: user.name,
          timeTracked: 0,
          timeSessions: 0,
          revisions: 0,
          characters: 0,
          contributionScore: 0
        };
      }
      
      unifiedUsers[user.name].revisions = user.totalRevisions;
      unifiedUsers[user.name].characters = user.totalCharacters;
    });

    // Calculate contribution scores
    const maxTime = Math.max(...Object.values(unifiedUsers).map(u => u.timeTracked), 1);
    const maxRevisions = Math.max(...Object.values(unifiedUsers).map(u => u.revisions), 1);
    const maxCharacters = Math.max(...Object.values(unifiedUsers).map(u => u.characters), 1);

    Object.values(unifiedUsers).forEach(user => {
      // Weighted contribution score (40% time, 30% revisions, 30% characters)
      user.contributionScore = (
        (user.timeTracked / maxTime) * 0.4 +
        (user.revisions / maxRevisions) * 0.3 +
        (user.characters / maxCharacters) * 0.3
      ) * 100;
    });

    // Sort by contribution score
    const sortedUsers = Object.values(unifiedUsers)
      .sort((a, b) => b.contributionScore - a.contributionScore);

    return {
      users: sortedUsers,
      totalUsers: sortedUsers.length,
      dataQuality: {
        hasTimeData: this.timeTrackingData.length > 0,
        hasRevisionData: !!this.revisionData,
        completeness: this.calculateDataCompleteness()
      },
      recommendations: this.generateRecommendations(sortedUsers)
    };
  }

  /**
   * Calculate data completeness score
   */
  calculateDataCompleteness() {
    let score = 0;
    
    if (this.timeTrackingData.length > 0) score += 50;
    if (this.revisionData) score += 50;
    
    return score;
  }

  /**
   * Generate payment recommendations
   */
  generateRecommendations(users) {
    const recommendations = [];
    
    users.forEach((user, index) => {
      const rank = index + 1;
      let recommendation = '';
      
      if (user.contributionScore > 80) {
        recommendation = 'Primary contributor - Full payment recommended';
      } else if (user.contributionScore > 50) {
        recommendation = 'Significant contributor - Substantial payment';
      } else if (user.contributionScore > 20) {
        recommendation = 'Moderate contributor - Partial payment';
      } else {
        recommendation = 'Minor contributor - Minimal payment';
      }
      
      recommendations.push({
        user: user.name,
        rank,
        score: user.contributionScore.toFixed(1),
        recommendation
      });
    });
    
    return recommendations;
  }

  /**
   * Export unified report as CSV
   */
  exportUnifiedCSV() {
    const report = this.generateUnifiedReport();
    
    let csvContent = 'User Name,Time Tracked (Hours),Sessions,Revisions,Characters,Contribution Score,Rank,Recommendation\n';
    
    report.unified.users.forEach((user, index) => {
      const timeHours = (user.timeTracked / (1000 * 60 * 60)).toFixed(2);
      csvContent += `"${user.name}",${timeHours},${user.timeSessions},${user.revisions},${user.characters},${user.contributionScore.toFixed(1)},${index + 1},"${report.unified.recommendations[index]?.recommendation || 'N/A'}"\n`;
    });
    
    return csvContent;
  }

  /**
   * Export detailed report as JSON
   */
  exportDetailedJSON() {
    return JSON.stringify(this.generateUnifiedReport(), null, 2);
  }
}

// Make it available globally
window.UnifiedReporter = UnifiedReporter;
