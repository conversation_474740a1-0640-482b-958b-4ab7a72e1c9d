// Google Docs API Client for fetching revision data
// Prevent duplicate class definition
if (typeof window.GoogleDocsAPIClient === 'undefined') {

class GoogleDocsAPIClient {
  constructor() {
    this.documentId = null;
    this.baseUrl = 'https://docs.google.com/document/d/';
    this.revisionCache = new Map();
    this.lastFetchTime = null;
    this.fetchInterval = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Initialize the API client with current document
   */
  init() {
    this.documentId = this.extractDocumentId();
    console.log('API Client initialized for document:', this.documentId);
  }

  /**
   * Extract document ID from current URL
   */
  extractDocumentId() {
    const url = window.location.href;
    const match = url.match(/\/document\/d\/([a-zA-Z0-9-_]+)/);
    return match ? match[1] : null;
  }

  /**
   * Get the revision API URL for the current document
   */
  getRevisionApiUrl(start = 1, batchSize = 1500, end = null) {
    if (!this.documentId) {
      throw new Error('No document ID available');
    }

    // Extract necessary parameters from the page
    const token = this.extractToken();

    let url = `${this.baseUrl}${this.documentId}/revisions/tiles?` +
              `id=${this.documentId}&` +
              `start=${start}&` +
              `revisionBatchSize=${batchSize}&` +
              `showDetailedRevisions=false&` +
              `loadType=0&` +
              `token=${token}&` +
              `includes_info_params=true&` +
              `cros_files=false&` +
              `tab=t.0`;

    // Add end parameter if specified (this is the key!)
    if (end !== null) {
      url += `&end=${end}`;
    }

    return url;
  }

  /**
   * Get the showrevision API URL (browser's actual endpoint)
   */
  getShowRevisionApiUrl(end = 6996) {
    if (!this.documentId) {
      throw new Error('No document ID available');
    }

    // Extract necessary parameters from the page
    const token = this.extractToken();

    return `${this.baseUrl}${this.documentId}/showrevision?` +
           `end=${end}&` +
           `id=${this.documentId}&` +
           `smv=69&` +
           `srfn=false&` +
           `ern=true&` +
           `smb=%5B2147483647%2C%20oAM%3D%5D&` +
           `token=${token}&` +
           `includes_info_params=true&` +
           `cros_files=false&` +
           `tab=t.0`;
  }



  /**
   * Extract authentication token from the page
   */
  extractToken() {
    // Try to find the token in various places
    const scripts = document.querySelectorAll('script');
    let token = null;

    for (const script of scripts) {
      const content = script.textContent || script.innerText;
      
      // Look for token patterns in the script content
      const tokenMatch = content.match(/["']token["']\s*:\s*["']([^"']+)["']/i) ||
                        content.match(/token[=:]\s*["']([^"']+)["']/i) ||
                        content.match(/AC4w5[a-zA-Z0-9_-]+/);
      
      if (tokenMatch) {
        token = tokenMatch[1] || tokenMatch[0];
        break;
      }
    }

    // Fallback: generate a basic token if none found
    if (!token) {
      const timestamp = Date.now();
      token = `AC4w5VjqsClhxOhMBAXP6He4ISyfV-hkqg:${timestamp}`;
    }

    return token;
  }

  /**
   * Fetch ALL revision data from Google Docs API with pagination
   */
  async fetchAllRevisionData(forceRefresh = false) {
    if (!this.documentId) {
      throw new Error('No document ID available');
    }

    // Check cache first
    const now = Date.now();
    if (!forceRefresh &&
        this.lastFetchTime &&
        (now - this.lastFetchTime) < this.fetchInterval &&
        this.revisionCache.has(this.documentId)) {
      console.log('Using cached revision data');
      return this.revisionCache.get(this.documentId);
    }

    try {
      console.log('Fetching ALL revision data for document:', this.documentId);

      let allRevisions = [];
      let allUsers = {};
      let start = 1; // Start from revision 1 to get complete history
      const batchSize = 500; // Smaller batches for more reliable fetching
      let hasMoreRevisions = true;
      let totalFetched = 0;

      console.log('🚀 ===== STARTING COMPREHENSIVE REVISION FETCH =====');
      console.log(`📋 Document ID: ${this.documentId}`);
      console.log(`🎯 Goal: Capture 100% of revision history including May 28, 2025 data`);
      console.log(`👥 Target: All 9 contributors (MEE DPMU TSR KSWMP, PIU CHAVAKKAD, ajith krishna, RESITHA K G, Sunil N, Sajil K, SCS Menon, KSWMP DPMU Thrissur, Anonymous users)`);
      console.log(`📅 Expected date range: May 28, 2025 onwards`);
      console.log(`🔧 Strategy: Browser mimicking approach with multiple API endpoints and range requests`);

      // PHASE 1: Try browser's actual showrevision endpoint first
      console.log('\n🔄 PHASE 1: Browser\'s Actual ShowRevision Endpoint');
      try {
        const showRevisionData = await this.fetchUsingShowRevision();
        if (showRevisionData && showRevisionData.tileInfo && showRevisionData.tileInfo.length > 0) {
          console.log(`🎯 ===== SHOWREVISION SUCCESS =====`);
          console.log(`📊 Found ${showRevisionData.tileInfo.length} revisions using browser's actual endpoint!`);

          allRevisions = showRevisionData.tileInfo;
          allUsers = showRevisionData.userMap || {};
          totalFetched = showRevisionData.tileInfo.length;

          // Analyze the complete data
          const dates = allRevisions.map(r => new Date(r.endMillis)).sort((a, b) => a - b);
          if (dates.length > 0) {
            const earliest = dates[0];
            const latest = dates[dates.length - 1];
            console.log(`📅 Complete date range: ${earliest.toLocaleString()} to ${latest.toLocaleString()}`);

            // Check specifically for May 28, 2025 data
            const may28Count = allRevisions.filter(r => {
              const date = new Date(r.endMillis);
              return date.getFullYear() === 2025 && date.getMonth() === 4 && date.getDate() === 28;
            }).length;
            console.log(`🎯 May 28, 2025 revisions found: ${may28Count}`);
          }

          const userNames = Object.values(allUsers).map(u => u.name || 'Anonymous');
          console.log(`👥 All users found: ${userNames.join(', ')}`);

          // Check for the 9 expected contributors
          const expectedContributors = ['MEE DPMU TSR KSWMP', 'PIU CHAVAKKAD', 'ajith krishna', 'RESITHA K G', 'Sunil N', 'Sajil K', 'SCS Menon', 'KSWMP DPMU Thrissur'];
          const foundContributors = expectedContributors.filter(name => userNames.includes(name));
          console.log(`🎯 Expected contributors found: ${foundContributors.length}/8 (${foundContributors.join(', ')})`);

          // Skip all other methods since we got complete data
          hasMoreRevisions = false;
          console.log(`✅ ShowRevision provided complete data - skipping other methods`);
        } else {
          console.log('❌ ShowRevision failed or returned no data, trying fallback methods...');
        }
      } catch (error) {
        console.error(`💥 ShowRevision error: ${error.message}, trying fallback methods...`);
      }

      // PHASE 2: Fallback to multi-range approach if showrevision failed
      if (totalFetched === 0) {
        console.log('\n🔄 PHASE 2: Fallback Multi-Range Approach');
        console.log('🎯 Goal: Capture complete revision history including May 28, 2025 data');
        console.log('📋 Using browser mimicking approach with multiple range requests...');

        // PHASE 2A: Use browser's sequential revision fetching pattern first
        console.log('\n🌐 PHASE 2A: Browser Mimicking Pattern');
        const browserMimicData = await this.fetchRevisionsLikeBrowser();
        if (browserMimicData && browserMimicData.tileInfo && browserMimicData.tileInfo.length > 0) {
          console.log(`🎯 Browser mimic found ${browserMimicData.tileInfo.length} revisions!`);
          allRevisions = browserMimicData.tileInfo;
          allUsers = browserMimicData.userMap || {};
          totalFetched = browserMimicData.tileInfo.length;

          // Check for May 28 data
          const may28Count = allRevisions.filter(r => {
            const date = new Date(r.endMillis);
            return date.getFullYear() === 2025 && date.getMonth() === 4 && date.getDate() === 28;
          }).length;
          console.log(`🎯 May 28, 2025 revisions found via browser mimic: ${may28Count}`);

          if (may28Count > 0) {
            console.log(`✅ Browser mimic successfully captured May 28 data - using this approach`);
            hasMoreRevisions = false; // Skip other methods
          }
        }

        // PHASE 2B: If browser mimic didn't get May 28 data, try specific range approach
        if (totalFetched === 0 || allRevisions.filter(r => {
          const date = new Date(r.endMillis);
          return date.getFullYear() === 2025 && date.getMonth() === 4 && date.getDate() === 28;
        }).length === 0) {
          console.log('\n🔄 PHASE 2B: Specific Range Approach for Missing Data');

          // First, try to get a small batch to see what the actual starting point is
          const testBatch = await this.fetchRevisionBatch(1, 10);
          if (testBatch && testBatch.tileInfo && testBatch.tileInfo.length > 0) {
            let firstRevisionStart = Math.min(...testBatch.tileInfo.map(r => r.start));
            console.log(`First available revision starts at position: ${firstRevisionStart}`);

            // If the first revision doesn't start at 1, we need to fetch earlier revisions
            if (firstRevisionStart > 1) {
              console.log(`Gap detected! Document likely has ${firstRevisionStart - 1} earlier characters/revisions.`);
              console.log('Using Google Docs multi-range strategy to fetch ALL earlier revisions...');

              // Strategy: Use multiple end parameters like Google does manually
              // Try different ranges working backwards from the gap, including the key ranges from memories
              const endRanges = [
                133,  // Key range from memories (start=1&end=133)
                firstRevisionStart - 1,  // Full gap
                Math.floor(firstRevisionStart * 0.75), // 75% of gap
                Math.floor(firstRevisionStart * 0.5),  // 50% of gap
                Math.floor(firstRevisionStart * 0.25), // 25% of gap
                1633, // Another common pattern
                3133  // Another common pattern
              ];

          for (const endRange of endRanges) {
            if (endRange > 0) {
              try {
                console.log(`Trying to fetch from start=1 to end=${endRange}`);
                const earlierBatch = await this.fetchRevisionBatchWithEnd(1, batchSize, endRange);
                if (earlierBatch && earlierBatch.tileInfo && earlierBatch.tileInfo.length > 0) {
                  console.log(`SUCCESS! Found ${earlierBatch.tileInfo.length} earlier revisions using end=${endRange}!`);

                  // Check if these are actually earlier revisions
                  const earliestInBatch = Math.min(...earlierBatch.tileInfo.map(r => r.start));

                  // Check for May 28, 2025 data specifically
                  const may28DataInBatch = earlierBatch.tileInfo.filter(r => {
                    const date = new Date(r.endMillis);
                    return date.getFullYear() === 2025 && date.getMonth() === 4 && date.getDate() === 28;
                  });

                  if (may28DataInBatch.length > 0) {
                    console.log(`🎯 FOUND MAY 28, 2025 DATA! ${may28DataInBatch.length} revisions from May 28 in range end=${endRange}`);
                    may28DataInBatch.forEach((rev, idx) => {
                      const date = new Date(rev.endMillis);
                      console.log(`   May 28 revision ${idx+1}: ${date.toLocaleString()}, positions ${rev.start}-${rev.end}`);
                    });
                  }

                  // Check for expected contributors
                  if (earlierBatch.userMap) {
                    const userNames = Object.values(earlierBatch.userMap).map(u => u.name || 'Anonymous');
                    const expectedContributors = ['MEE DPMU TSR KSWMP', 'PIU CHAVAKKAD', 'ajith krishna', 'RESITHA K G', 'Sunil N', 'Sajil K', 'SCS Menon', 'KSWMP DPMU Thrissur'];
                    const foundContributors = expectedContributors.filter(name => userNames.includes(name));
                    console.log(`👥 Expected contributors in this batch: ${foundContributors.length}/8 (${foundContributors.join(', ')})`);
                  }

                  if (earliestInBatch < firstRevisionStart) {
                    console.log(`Confirmed earlier revisions found starting at position ${earliestInBatch}`);
                    allRevisions = allRevisions.concat(earlierBatch.tileInfo);
                    if (earlierBatch.userMap) {
                      allUsers = { ...allUsers, ...earlierBatch.userMap };
                    }
                    totalFetched += earlierBatch.tileInfo.length;

                    // Update the first revision start for next iterations
                    firstRevisionStart = earliestInBatch;
                  }
                } else {
                  console.log(`No revisions found with end=${endRange}`);
                }
              } catch (error) {
                console.log(`Failed to fetch with end=${endRange}: ${error.message}`);
              }

              // Small delay between requests
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          }



          // Strategy 4: Try fetching with different batch sizes and ranges
          console.log('Trying different batch sizes and ranges...');
          for (let batchSize of [50, 100, 1000, 2000]) {
            try {
              console.log(`Trying batch size ${batchSize} from position 1...`);
              const batchData = await this.fetchRevisionBatch(1, batchSize);
              if (batchData && batchData.tileInfo && batchData.tileInfo.length > 0) {
                const earliestPos = Math.min(...batchData.tileInfo.map(r => r.start));
                if (earliestPos < firstRevisionStart) {
                  console.log(`SUCCESS! Found earlier revisions starting at position ${earliestPos} with batch size ${batchSize}!`);
                  allRevisions = allRevisions.concat(batchData.tileInfo);
                  if (batchData.userMap) {
                    allUsers = { ...allUsers, ...batchData.userMap };
                  }
                  totalFetched += batchData.tileInfo.length;
                  break;
                }
              }
            } catch (error) {
              console.log(`Failed with batch size ${batchSize}: ${error.message}`);
            }
          }
        }
      }
      }

      while (hasMoreRevisions) {
        console.log(`Fetching revisions batch starting at ${start}...`);

        const batchData = await this.fetchRevisionBatch(start, batchSize);

        if (batchData && batchData.tileInfo && batchData.tileInfo.length > 0) {
          allRevisions = allRevisions.concat(batchData.tileInfo);

          // Merge user maps
          if (batchData.userMap) {
            allUsers = { ...allUsers, ...batchData.userMap };
          }

          totalFetched += batchData.tileInfo.length;
          console.log(`Fetched ${batchData.tileInfo.length} revisions in this batch. Total: ${totalFetched}`);

          // Get the earliest revision position to continue fetching backwards
          const earliestRevision = Math.min(...batchData.tileInfo.map(r => r.start));
          console.log(`Earliest revision in this batch starts at position: ${earliestRevision}`);

          // Check if we got fewer revisions than requested (indicates end)
          if (batchData.tileInfo.length < batchSize) {
            console.log('Received fewer revisions than requested - likely reached the end');
            hasMoreRevisions = false;
          } else {
            // Continue fetching from the next batch
            start += batchSize;
            console.log(`Continuing to fetch from position: ${start}`);
          }

          // Add a small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 200));
        } else {
          console.log('No more revision data available');
          hasMoreRevisions = false;
        }

        // Safety check to prevent infinite loops
        if (totalFetched > 10000) {
          console.warn('Reached maximum revision limit (10,000). Stopping fetch.');
          break;
        }
      }
      }

      // Advanced sorting to ensure proper chronological order
      console.log(`🔄 Sorting ${allRevisions.length} revisions for chronological order...`);
      allRevisions.sort((a, b) => {
        // Primary sort: by start position
        if (a.start !== b.start) {
          return a.start - b.start;
        }
        // Secondary sort: by timestamp for same positions
        if (a.endMillis !== b.endMillis) {
          return a.endMillis - b.endMillis;
        }
        // Tertiary sort: by end position
        return a.end - b.end;
      });

      // Remove any remaining duplicates after sorting
      const uniqueRevisions = [];
      const seenKeys = new Set();

      for (const revision of allRevisions) {
        const key = `${revision.start}-${revision.end}-${revision.endMillis}`;
        if (!seenKeys.has(key)) {
          seenKeys.add(key);
          uniqueRevisions.push(revision);
        }
      }

      if (uniqueRevisions.length !== allRevisions.length) {
        console.log(`🔄 Removed ${allRevisions.length - uniqueRevisions.length} duplicate revisions after sorting`);
        allRevisions = uniqueRevisions;
      }

      console.log(`✅ Final revision count after deduplication: ${allRevisions.length}`);

      const completeRevisionData = {
        tileInfo: allRevisions,
        userMap: allUsers,
        firstRev: allRevisions.length > 0 ? Math.min(...allRevisions.map(r => r.start)) : 0,
        totalRevisions: allRevisions.length
      };

      console.log('Complete revision data prepared:', {
        totalRevisions: allRevisions.length,
        firstRevisionPosition: completeRevisionData.firstRev,
        lastRevisionPosition: allRevisions.length > 0 ? Math.max(...allRevisions.map(r => r.end)) : 0,
        dateRange: this.getDateRange(allRevisions)
      });

      // Cache the result
      this.revisionCache.set(this.documentId, completeRevisionData);
      this.lastFetchTime = now;

      console.log('Successfully fetched ALL revision data:', {
        totalRevisions: allRevisions.length,
        users: Object.keys(allUsers).length,
        dateRange: this.getDateRange(allRevisions)
      });

      return completeRevisionData;

    } catch (error) {
      console.error('Error fetching revision data:', error);
      throw error;
    }
  }

  /**
   * Fetch a single batch of revisions
   */
  async fetchRevisionBatch(start, batchSize) {
    try {
      const apiUrl = this.getRevisionApiUrl(start, batchSize);
      console.log(`Fetching batch: ${apiUrl}`);

      const response = await fetch(apiUrl, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const text = await response.text();

      // Google Docs API often returns JSONP-style responses
      let jsonData = null;

      if (text.startsWith(')]}\'')) {
        // Remove security prefix
        jsonData = JSON.parse(text.substring(4));
      } else if (text.includes('{"tileInfo"')) {
        // Extract JSON from JSONP response
        const jsonMatch = text.match(/\{.*\}/);
        if (jsonMatch) {
          jsonData = JSON.parse(jsonMatch[0]);
        }
      } else {
        jsonData = JSON.parse(text);
      }

      return jsonData;

    } catch (error) {
      console.warn(`Failed to fetch batch starting at ${start}:`, error);
      return null;
    }
  }

  /**
   * Fetch revisions with end parameter (like Google does manually)
   */
  async fetchRevisionBatchWithEnd(start, batchSize, end) {
    try {
      const apiUrl = this.getRevisionApiUrl(start, batchSize, end);
      console.log(`Fetching batch with end parameter: ${apiUrl}`);

      const response = await fetch(apiUrl, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'DNT': '1',
          'Priority': 'u=1, i',
          'Referer': `https://docs.google.com/document/d/${this.documentId}/edit?tab=t.0`,
          'Sec-CH-UA': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'Sec-CH-UA-Mobile': '?0',
          'Sec-CH-UA-Platform': '"macOS"',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'X-Same-Domain': '1'
        }
      });

      if (!response.ok) {
        throw new Error(`API request with end parameter failed: ${response.status}`);
      }

      const text = await response.text();

      // Parse the response
      let jsonData = null;

      if (text.startsWith(')]}\'')) {
        jsonData = JSON.parse(text.substring(4));
      } else if (text.includes('{"tileInfo"')) {
        const jsonMatch = text.match(/\{.*\}/);
        if (jsonMatch) {
          jsonData = JSON.parse(jsonMatch[0]);
        }
      } else {
        jsonData = JSON.parse(text);
      }

      return jsonData;

    } catch (error) {
      console.warn(`Failed to fetch batch with end parameter (${start}-${end}):`, error);
      return null;
    }
  }

  /**
   * Fetch using browser's actual showrevision endpoint
   */
  async fetchUsingShowRevision(end = 6996) {
    try {
      const apiUrl = this.getShowRevisionApiUrl(end);
      console.log(`🌐 Fetching using browser's actual endpoint: ${apiUrl}`);

      const response = await fetch(apiUrl, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'accept': '*/*',
          'accept-language': 'en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
          'dnt': '1',
          'priority': 'u=1, i',
          'referer': `https://docs.google.com/document/d/${this.documentId}/edit?tab=t.0`,
          'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-origin',
          'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'x-build': 'editors.documents-frontend_20250610.02_p5',
          'x-client-data': 'CJK2yQEIpbbJAQipncoBCJvpygEIk6HLAQiSo8sBCIagzQEIzO3OAQiw8c4BCJDyzgE=',
          'x-client-deadline-ms': '360000',
          'x-rel-id': '2bf.16863bd.s',
          'x-same-domain': '1'
        }
      });

      if (!response.ok) {
        throw new Error(`ShowRevision API request failed: ${response.status}`);
      }

      const text = await response.text();
      console.log(`📊 ShowRevision response length: ${text.length} characters`);

      // Parse the response
      let jsonData = null;

      if (text.startsWith(')]}\'')) {
        jsonData = JSON.parse(text.substring(4));
      } else if (text.includes('{"tileInfo"')) {
        const jsonMatch = text.match(/\{.*\}/);
        if (jsonMatch) {
          jsonData = JSON.parse(jsonMatch[0]);
        }
      } else {
        jsonData = JSON.parse(text);
      }

      console.log(`✅ ShowRevision parsed successfully:`, {
        hasRevisions: !!(jsonData && jsonData.tileInfo),
        revisionCount: jsonData && jsonData.tileInfo ? jsonData.tileInfo.length : 0,
        hasUsers: !!(jsonData && jsonData.userMap),
        userCount: jsonData && jsonData.userMap ? Object.keys(jsonData.userMap).length : 0
      });

      return jsonData;

    } catch (error) {
      console.error(`❌ ShowRevision API failed:`, error);
      return null;
    }
  }

  /**
   * Mimic browser's sequential revision fetching pattern with detailed logging
   */
  async fetchRevisionsLikeBrowser() {
    console.log('🔄 ===== STARTING BROWSER MIMICKING PATTERN =====');
    console.log('🎯 Target: Capture complete revision history including May 28 data');

    let allRevisions = [];
    let allUsers = {};

    // Browser pattern: Start with common ranges that work, prioritizing May 28 data
    const browserRanges = [
      { start: 1, end: 133, desc: "Critical early range (May 28, 2025 area)", priority: "HIGH" },
      { start: 1, end: 500, desc: "Extended early range (May-June 2025)", priority: "HIGH" },
      { start: 1, end: 1633, desc: "Comprehensive early content", priority: "MEDIUM" },
      { start: 1, end: 3133, desc: "Full early history", priority: "MEDIUM" },
      { start: 1, end: 6996, desc: "Complete document range", priority: "LOW" },
      { start: 1, end: null, desc: "Current revisions (Latest data)", priority: "LOW" }
    ];

    console.log(`📋 Will attempt ${browserRanges.length} different range requests (prioritizing May 28 data):`);
    browserRanges.forEach((range, i) => {
      console.log(`   ${i+1}. [${range.priority}] ${range.desc}: start=${range.start}, end=${range.end || 'current'}`);
    });

    let may28DataFound = false;
    let expectedContributorsFound = 0;

    for (let i = 0; i < browserRanges.length; i++) {
      const range = browserRanges[i];
      console.log(`\n🌐 ===== RANGE ${i+1}/${browserRanges.length}: [${range.priority}] ${range.desc} =====`);
      console.log(`📡 Request parameters: start=${range.start}, end=${range.end || 'current'}, batchSize=1500`);

      // If we already found May 28 data and expected contributors, skip low priority ranges
      if (may28DataFound && expectedContributorsFound >= 6 && range.priority === 'LOW') {
        console.log(`⏭️ Skipping low priority range - already found May 28 data and ${expectedContributorsFound} contributors`);
        continue;
      }

      try {
        let batchData;
        const startTime = Date.now();

        if (range.end) {
          console.log(`🔗 Making API call with END parameter: fetchRevisionBatchWithEnd(${range.start}, 1500, ${range.end})`);
          batchData = await this.fetchRevisionBatchWithEnd(range.start, 1500, range.end);
        } else {
          console.log(`🔗 Making API call WITHOUT end parameter: fetchRevisionBatch(${range.start}, 1500)`);
          batchData = await this.fetchRevisionBatch(range.start, 1500);
        }

        const requestTime = Date.now() - startTime;
        console.log(`⏱️ Request completed in ${requestTime}ms`);

        if (batchData && batchData.tileInfo && batchData.tileInfo.length > 0) {
          console.log(`✅ SUCCESS: Found ${batchData.tileInfo.length} revisions in ${range.desc}`);

          // Analyze the revisions found
          const revisionStarts = batchData.tileInfo.map(r => r.start);
          const revisionEnds = batchData.tileInfo.map(r => r.end);
          const timestamps = batchData.tileInfo.map(r => r.endMillis);

          console.log(`📊 Revision analysis:`);
          console.log(`   - Position range: ${Math.min(...revisionStarts)} to ${Math.max(...revisionEnds)}`);
          console.log(`   - Timestamp range: ${Math.min(...timestamps)} to ${Math.max(...timestamps)}`);

          // Convert timestamps to readable dates
          const earliestDate = new Date(Math.min(...timestamps));
          const latestDate = new Date(Math.max(...timestamps));
          console.log(`   - Date range: ${earliestDate.toLocaleString()} to ${latestDate.toLocaleString()}`);

          // Check for May 28, 2025 data specifically
          const may28Data = batchData.tileInfo.filter(r => {
            const date = new Date(r.endMillis);
            return date.getFullYear() === 2025 && date.getMonth() === 4 && date.getDate() === 28; // May = month 4
          });

          if (may28Data.length > 0) {
            console.log(`🎯 FOUND MAY 28, 2025 DATA! ${may28Data.length} revisions from May 28, 2025`);
            may28DataFound = true;
            may28Data.forEach((rev, idx) => {
              const date = new Date(rev.endMillis);
              console.log(`   May 28 revision ${idx+1}: ${date.toLocaleString()}, positions ${rev.start}-${rev.end}`);
            });
          } else {
            console.log(`❌ No May 28, 2025 data found in this range`);
          }

          // Check users in this batch
          if (batchData.userMap) {
            const userNames = Object.values(batchData.userMap).map(u => u.name || 'Anonymous');
            console.log(`👥 Users in this batch: ${userNames.join(', ')}`);

            // Check for all expected contributors
            const expectedContributors = ['MEE DPMU TSR KSWMP', 'PIU CHAVAKKAD', 'ajith krishna', 'RESITHA K G', 'Sunil N', 'Sajil K', 'SCS Menon', 'KSWMP DPMU Thrissur'];
            const foundContributors = expectedContributors.filter(name => userNames.includes(name));
            expectedContributorsFound = Math.max(expectedContributorsFound, foundContributors.length);

            console.log(`🎯 Expected contributors in this batch: ${foundContributors.length}/8 (${foundContributors.join(', ')})`);
            console.log(`📊 Total expected contributors found so far: ${expectedContributorsFound}/8`);

            // Special check for KSWMP DPMU Thrissur
            const kswmpUser = Object.values(batchData.userMap).find(u => u.name === 'KSWMP DPMU Thrissur');
            if (kswmpUser) {
              console.log(`🎯 FOUND KSWMP DPMU Thrissur user in this batch!`);
            }
          }

          // Advanced revision merging with duplicate detection and integrity checks
          const existingRevisionKeys = new Set(allRevisions.map(r => `${r.start}-${r.end}-${r.endMillis}`));
          const newRevisions = batchData.tileInfo.filter(r => {
            const revisionKey = `${r.start}-${r.end}-${r.endMillis}`;
            return !existingRevisionKeys.has(revisionKey);
          });
          const duplicateCount = batchData.tileInfo.length - newRevisions.length;

          console.log(`🔄 Advanced merging: ${newRevisions.length} new revisions, ${duplicateCount} duplicates filtered`);

          if (newRevisions.length > 0) {
            // Validate revision integrity before merging
            const validRevisions = newRevisions.filter(r => {
              return r.start !== undefined && r.end !== undefined && r.endMillis !== undefined && r.users && r.users.length > 0;
            });

            if (validRevisions.length !== newRevisions.length) {
              console.log(`⚠️ Filtered out ${newRevisions.length - validRevisions.length} invalid revisions`);
            }

            allRevisions = allRevisions.concat(validRevisions);
            console.log(`📊 Total revisions after merge: ${allRevisions.length}`);
          }

          if (batchData.userMap) {
            const newUsers = Object.keys(batchData.userMap).filter(id => !allUsers[id]);
            const updatedUsers = Object.keys(batchData.userMap).filter(id => {
              return allUsers[id] && (!allUsers[id].name || allUsers[id].name === 'Anonymous') && batchData.userMap[id].name;
            });

            console.log(`👥 Merging users: ${newUsers.length} new users, ${updatedUsers.length} updated users`);

            // Merge with preference for non-anonymous names
            Object.keys(batchData.userMap).forEach(userId => {
              const existingUser = allUsers[userId];
              const newUser = batchData.userMap[userId];

              if (!existingUser) {
                allUsers[userId] = newUser;
              } else if (existingUser.name === 'Anonymous' && newUser.name && newUser.name !== 'Anonymous') {
                // Prefer non-anonymous names
                allUsers[userId] = { ...existingUser, ...newUser };
              } else if (!existingUser.name && newUser.name) {
                // Fill in missing names
                allUsers[userId] = { ...existingUser, ...newUser };
              }
            });

            console.log(`📊 Total unique users: ${Object.keys(allUsers).length}`);
          }

          console.log(`📊 Running totals: ${allRevisions.length} total revisions, ${Object.keys(allUsers).length} total users`);
        } else {
          console.log(`❌ FAILED: No revisions found in ${range.desc}`);
          if (batchData) {
            console.log(`   Response structure:`, Object.keys(batchData));
          } else {
            console.log(`   No response data received`);
          }
        }

        // Browser-like delay between requests
        console.log(`⏳ Waiting 200ms before next request...`);
        await new Promise(resolve => setTimeout(resolve, 200));

      } catch (error) {
        console.error(`💥 ERROR in ${range.desc}:`, error.message);
        console.error(`   Full error:`, error);
      }
    }

    // Final analysis
    console.log(`\n🎯 ===== BROWSER MIMIC COMPLETE =====`);
    console.log(`📊 Final results: ${allRevisions.length} total revisions found`);

    if (allRevisions.length > 0) {
      // Advanced sorting: first by start position, then by timestamp for same positions
      allRevisions.sort((a, b) => {
        if (a.start !== b.start) {
          return a.start - b.start;
        }
        return a.endMillis - b.endMillis;
      });

      console.log(`🔄 Final sorting complete: ${allRevisions.length} revisions in chronological order`);

      // Analyze final date range
      const allTimestamps = allRevisions.map(r => r.endMillis);
      const earliestDate = new Date(Math.min(...allTimestamps));
      const latestDate = new Date(Math.max(...allTimestamps));

      console.log(`📅 Complete date range: ${earliestDate.toLocaleString()} to ${latestDate.toLocaleString()}`);
      console.log(`📍 Position range: ${Math.min(...allRevisions.map(r => r.start))} to ${Math.max(...allRevisions.map(r => r.end))}`);

      // Final comprehensive analysis
      const finalMay28Data = allRevisions.filter(r => {
        const date = new Date(r.endMillis);
        return date.getFullYear() === 2025 && date.getMonth() === 4 && date.getDate() === 28;
      });

      if (finalMay28Data.length > 0) {
        console.log(`🎯 SUCCESS: Found ${finalMay28Data.length} May 28, 2025 revisions in final dataset!`);
        console.log(`📊 May 28 revision details:`);
        finalMay28Data.forEach((rev, idx) => {
          const date = new Date(rev.endMillis);
          const users = rev.users.map(uid => allUsers[uid]?.name || 'Unknown').join(', ');
          console.log(`   ${idx+1}. ${date.toLocaleString()} | Users: ${users} | Pos: ${rev.start}-${rev.end}`);
        });
      } else {
        console.log(`❌ CRITICAL PROBLEM: No May 28, 2025 data in final dataset!`);
        console.log(`🔍 This indicates the revision fetching is not capturing the complete history`);
      }

      // List all users found with detailed analysis
      const allUserNames = Object.values(allUsers).map(u => u.name || 'Anonymous');
      console.log(`👥 All users found (${allUserNames.length} total): ${allUserNames.join(', ')}`);

      // Check for expected contributors
      const expectedContributors = ['MEE DPMU TSR KSWMP', 'PIU CHAVAKKAD', 'ajith krishna', 'RESITHA K G', 'Sunil N', 'Sajil K', 'SCS Menon', 'KSWMP DPMU Thrissur'];
      const foundExpectedContributors = expectedContributors.filter(name => allUserNames.includes(name));
      console.log(`🎯 Expected contributors found: ${foundExpectedContributors.length}/8`);
      console.log(`   Found: ${foundExpectedContributors.join(', ')}`);

      if (foundExpectedContributors.length < 8) {
        const missingContributors = expectedContributors.filter(name => !allUserNames.includes(name));
        console.log(`❌ Missing contributors: ${missingContributors.join(', ')}`);
      }

      // Date range analysis
      if (allRevisions.length > 0) {
        const allDates = allRevisions.map(r => new Date(r.endMillis));
        const earliestDate = new Date(Math.min(...allDates));
        const latestDate = new Date(Math.max(...allDates));
        console.log(`� Complete date coverage: ${earliestDate.toLocaleString()} to ${latestDate.toLocaleString()}`);

        // Check if we have data from May 28, 2025 onwards as expected
        const may28_2025 = new Date(2025, 4, 28); // May 28, 2025
        if (earliestDate <= may28_2025) {
          console.log(`✅ SUCCESS: Data coverage includes May 28, 2025 onwards as expected`);
        } else {
          console.log(`❌ PROBLEM: Data coverage starts after May 28, 2025 (starts ${earliestDate.toLocaleString()})`);
        }
      }
    } else {
      console.log(`�💥 CRITICAL: No revisions found at all!`);
      console.log(`🔍 This indicates a complete failure in revision fetching - check API endpoints and authentication`);
    }

    return {
      tileInfo: allRevisions,
      userMap: allUsers,
      firstRev: allRevisions.length > 0 ? Math.min(...allRevisions.map(r => r.start)) : 0,
      totalRevisions: allRevisions.length
    };
  }

  /**
   * Get date range from revisions
   */
  getDateRange(revisions) {
    if (!revisions || revisions.length === 0) {
      return 'No revisions';
    }

    const dates = revisions.map(r => new Date(r.endMillis)).sort((a, b) => a - b);
    const earliest = dates[0].toLocaleDateString();
    const latest = dates[dates.length - 1].toLocaleDateString();

    return `${earliest} to ${latest}`;
  }

  /**
   * Legacy method for backward compatibility
   */
  async fetchRevisionData(forceRefresh = false) {
    return this.fetchAllRevisionData(forceRefresh);
  }

  /**
   * Get cached revision data if available
   */
  getCachedRevisionData() {
    if (this.documentId && this.revisionCache.has(this.documentId)) {
      return this.revisionCache.get(this.documentId);
    }
    return null;
  }

  /**
   * Clear cache for current document
   */
  clearCache() {
    if (this.documentId) {
      this.revisionCache.delete(this.documentId);
      this.lastFetchTime = null;
    }
  }

  /**
   * Check if revision data is available for current document
   */
  hasRevisionData() {
    return this.documentId && this.revisionCache.has(this.documentId);
  }

  /**
   * Get document metadata
   */
  getDocumentMetadata() {
    const title = document.title.replace(' - Google Docs', '');
    const url = window.location.href;
    
    return {
      id: this.documentId,
      title: title,
      url: url,
      lastAccessed: new Date().toISOString()
    };
  }
}

// Make it available globally
window.GoogleDocsAPIClient = GoogleDocsAPIClient;

} // End of duplicate prevention check
