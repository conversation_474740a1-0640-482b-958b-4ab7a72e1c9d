// Test script for verifying revision fetching functionality
// This script can be run in the browser console on a Google Docs page

console.log('🧪 ===== REVISION FETCHING TEST SCRIPT =====');
console.log('🎯 Testing complete revision history capture including May 28, 2025 data');

async function testRevisionFetching() {
  try {
    // Check if API client is available
    if (!window.GoogleDocsAPIClient) {
      console.error('❌ GoogleDocsAPIClient not found. Make sure the extension is loaded.');
      return;
    }

    // Initialize API client
    const apiClient = new GoogleDocsAPIClient();
    apiClient.init();

    console.log('✅ API Client initialized');
    console.log(`📋 Document ID: ${apiClient.documentId}`);

    // Test 1: Fetch all revision data
    console.log('\n🧪 TEST 1: Fetching all revision data...');
    const revisionData = await apiClient.fetchAllRevisionData(true);

    if (!revisionData || !revisionData.tileInfo) {
      console.error('❌ TEST 1 FAILED: No revision data returned');
      return;
    }

    console.log(`✅ TEST 1 PASSED: Retrieved ${revisionData.tileInfo.length} revisions`);

    // Test 2: Check for May 28, 2025 data
    console.log('\n🧪 TEST 2: Checking for May 28, 2025 data...');
    const may28Data = revisionData.tileInfo.filter(r => {
      const date = new Date(r.endMillis);
      return date.getFullYear() === 2025 && date.getMonth() === 4 && date.getDate() === 28;
    });

    if (may28Data.length > 0) {
      console.log(`✅ TEST 2 PASSED: Found ${may28Data.length} revisions from May 28, 2025`);
      may28Data.forEach((rev, idx) => {
        const date = new Date(rev.endMillis);
        console.log(`   ${idx+1}. ${date.toLocaleString()} | Pos: ${rev.start}-${rev.end}`);
      });
    } else {
      console.error('❌ TEST 2 FAILED: No May 28, 2025 data found');
    }

    // Test 3: Check for expected contributors
    console.log('\n🧪 TEST 3: Checking for expected contributors...');
    const expectedContributors = ['MEE DPMU TSR KSWMP', 'PIU CHAVAKKAD', 'ajith krishna', 'RESITHA K G', 'Sunil N', 'Sajil K', 'SCS Menon', 'KSWMP DPMU Thrissur'];
    const userNames = Object.values(revisionData.userMap || {}).map(u => u.name || 'Anonymous');
    const foundContributors = expectedContributors.filter(name => userNames.includes(name));

    console.log(`📊 Expected contributors found: ${foundContributors.length}/8`);
    console.log(`   Found: ${foundContributors.join(', ')}`);

    if (foundContributors.length >= 6) {
      console.log(`✅ TEST 3 PASSED: Found ${foundContributors.length}/8 expected contributors`);
    } else {
      console.error(`❌ TEST 3 FAILED: Only found ${foundContributors.length}/8 expected contributors`);
    }

    // Test 4: Test revision analysis
    console.log('\n🧪 TEST 4: Testing revision analysis...');
    if (!window.RevisionAnalyzer) {
      console.error('❌ TEST 4 FAILED: RevisionAnalyzer not found');
      return;
    }

    const analyzer = new RevisionAnalyzer();
    const analysisResults = analyzer.parseRevisionData(revisionData);

    if (analysisResults && analysisResults.userContributions) {
      console.log(`✅ TEST 4 PASSED: Analysis completed successfully`);
      console.log(`📊 Analysis summary: ${analysisResults.summary.totalRevisions} revisions, ${analysisResults.summary.totalUsers} users`);
    } else {
      console.error('❌ TEST 4 FAILED: Analysis failed');
    }

    // Test 5: Test CSV export
    console.log('\n🧪 TEST 5: Testing CSV export...');
    try {
      const comprehensiveCSV = analyzer.exportComprehensiveCSV();
      const userSummaryCSV = analyzer.exportUserSummaryCSV();
      const timelineCSV = analyzer.exportTimelineCSV();

      console.log(`✅ TEST 5 PASSED: CSV exports generated successfully`);
      console.log(`📊 Comprehensive CSV length: ${comprehensiveCSV.length} characters`);
      console.log(`📊 User Summary CSV length: ${userSummaryCSV.length} characters`);
      console.log(`📊 Timeline CSV length: ${timelineCSV.length} characters`);

      // Check if May 28 data is in CSV
      const may28InCSV = comprehensiveCSV.includes('2025') && comprehensiveCSV.includes('5/28/') || comprehensiveCSV.includes('28/5/');
      if (may28InCSV) {
        console.log(`✅ May 28, 2025 data confirmed in CSV export`);
      } else {
        console.error(`❌ May 28, 2025 data NOT found in CSV export`);
      }

    } catch (error) {
      console.error(`❌ TEST 5 FAILED: CSV export error: ${error.message}`);
    }

    // Final summary
    console.log('\n🎯 ===== TEST SUMMARY =====');
    console.log(`📊 Total revisions: ${revisionData.tileInfo.length}`);
    console.log(`📅 May 28, 2025 revisions: ${may28Data.length}`);
    console.log(`👥 Expected contributors: ${foundContributors.length}/8`);
    console.log(`📋 All users: ${userNames.length} total`);

    if (revisionData.tileInfo.length > 0) {
      const dates = revisionData.tileInfo.map(r => new Date(r.endMillis)).sort((a, b) => a - b);
      console.log(`📅 Date range: ${dates[0].toLocaleString()} to ${dates[dates.length - 1].toLocaleString()}`);
    }

    return {
      success: true,
      totalRevisions: revisionData.tileInfo.length,
      may28Revisions: may28Data.length,
      foundContributors: foundContributors.length,
      totalUsers: userNames.length
    };

  } catch (error) {
    console.error('💥 TEST FAILED with error:', error);
    return { success: false, error: error.message };
  }
}

// Auto-run the test if this script is executed
if (typeof window !== 'undefined' && window.location.href.includes('docs.google.com')) {
  console.log('🚀 Auto-running revision fetching test...');
  testRevisionFetching().then(result => {
    if (result.success) {
      console.log('🎉 ALL TESTS COMPLETED - Check results above');
    } else {
      console.error('💥 TESTS FAILED:', result.error);
    }
  });
} else {
  console.log('📝 Test script loaded. Run testRevisionFetching() to execute tests.');
}

// Make test function available globally
window.testRevisionFetching = testRevisionFetching;
