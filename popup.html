<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <link rel="stylesheet" href="popup.css">
  <style>
    body {
      width: 450px;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      margin: 0;
      background: #f8f9fa;
    }

    .header {
      background: #1a73e8;
      color: white;
      padding: 15px;
      text-align: center;
      margin: 0;
    }

    .header h3 {
      margin: 0;
      font-size: 16px;
    }

    .tabs {
      display: flex;
      background: white;
      border-bottom: 1px solid #e0e0e0;
    }

    .tab {
      flex: 1;
      padding: 12px 8px;
      text-align: center;
      cursor: pointer;
      border: none;
      background: none;
      font-size: 13px;
      color: #5f6368;
      border-bottom: 2px solid transparent;
    }

    .tab.active {
      color: #1a73e8;
      border-bottom-color: #1a73e8;
      font-weight: 500;
    }

    .tab:hover {
      background: #f8f9fa;
    }

    .tab-content {
      display: none;
      padding: 15px;
      background: white;
    }

    .tab-content.active {
      display: block;
    }

    .status {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding: 10px;
      border-radius: 6px;
      background: #f5f5f5;
    }

    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .active-indicator { background-color: #34a853; }
    .inactive-indicator { background-color: #ea4335; }

    .user-info {
      margin-bottom: 15px;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 6px;
      font-size: 13px;
    }

    .time-summary {
      margin-bottom: 20px;
    }

    .time-entry {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid #e0e0e0;
    }

    .time-entry:last-child {
      border-bottom: none;
    }

    .user-name {
      font-weight: 500;
      color: #1a73e8;
    }

    .time-duration {
      color: #5f6368;
      font-family: monospace;
    }

    .controls {
      display: flex;
      gap: 8px;
      margin-bottom: 15px;
      flex-wrap: wrap;
    }

    button {
      padding: 8px 12px;
      border: 1px solid #dadce0;
      border-radius: 4px;
      background: white;
      cursor: pointer;
      font-size: 12px;
      flex: 1;
      min-width: 100px;
    }

    button:hover {
      background: #f8f9fa;
    }

    .primary-btn {
      background: #1a73e8;
      color: white;
      border-color: #1a73e8;
    }

    .primary-btn:hover {
      background: #1557b0;
    }

    .success-btn {
      background: #34a853;
      color: white;
      border-color: #34a853;
    }

    .success-btn:hover {
      background: #2d8f47;
    }

    .export-section {
      border-top: 1px solid #e0e0e0;
      padding-top: 15px;
      margin-top: 15px;
    }

    .total-time {
      font-size: 14px;
      font-weight: 600;
      color: #1a73e8;
      text-align: center;
      margin: 15px 0;
      padding: 8px;
      background: #e8f0fe;
      border-radius: 6px;
    }

    .loading {
      text-align: center;
      color: #5f6368;
      padding: 20px;
    }

    .revision-summary {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 6px;
      margin-bottom: 15px;
      font-size: 13px;
    }

    .revision-user {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #e0e0e0;
    }

    .revision-user:last-child {
      border-bottom: none;
    }

    .user-contribution {
      font-size: 12px;
      color: #5f6368;
    }

    .help-section {
      max-height: 400px;
      overflow-y: auto;
    }

    .help-item {
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #e0e0e0;
    }

    .help-item:last-child {
      border-bottom: none;
    }

    .help-title {
      font-weight: 600;
      color: #1a73e8;
      margin-bottom: 8px;
    }

    .help-content {
      font-size: 13px;
      line-height: 1.4;
      color: #5f6368;
    }

    .help-steps {
      list-style: none;
      padding: 0;
      margin: 8px 0;
    }

    .help-steps li {
      padding: 4px 0;
      padding-left: 20px;
      position: relative;
    }

    .help-steps li:before {
      content: counter(step-counter);
      counter-increment: step-counter;
      position: absolute;
      left: 0;
      top: 4px;
      background: #1a73e8;
      color: white;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      font-size: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .help-steps {
      counter-reset: step-counter;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="header-content">
      <img src="https://www.ecogo.ai/wp-content/uploads/2024/11/cropped-logo1-300x81.png" alt="ecogo.ai" class="logo">
      <h3>Time Tracker</h3>
    </div>
  </div>

  <div class="tabs">
    <button class="tab active" data-tab="overview">Overview</button>
    <button class="tab" data-tab="revisions">All Revisions</button>
    <button class="tab" data-tab="export">Export Data</button>
    <button class="tab" data-tab="help">Help</button>
  </div>

  <!-- Overview Tab -->
  <div class="tab-content active" id="overview">
    <div class="user-info" id="userInfo">
      <strong>User:</strong> <span id="currentUser">Loading...</span><br>
      <strong>Document:</strong> <span id="documentTitle">Loading...</span><br>
      <strong>ID:</strong> <span id="documentId">Loading...</span>
    </div>

    <div class="controls">
      <button id="fetchAllRevisionsBtn" class="primary-btn">Fetch ALL Revisions</button>
      <button id="refreshBtn">Refresh</button>
    </div>

    <div class="revision-summary" id="revisionOverview">
      <div class="loading">Click "Fetch ALL Revisions" to load complete revision history</div>
    </div>
  </div>

  <!-- All Revisions Tab -->
  <div class="tab-content" id="revisions">
    <div class="controls">
      <button id="analyzeRevisionsBtn" class="success-btn">Analyze All Revisions</button>
      <button id="showTimelineBtn">Show Timeline</button>
    </div>

    <div class="revision-summary" id="revisionSummary">
      <div class="loading">Fetch revisions first, then click "Analyze All Revisions"</div>
    </div>

    <div id="revisionDetails" style="display: none;">
      <!-- Detailed revision analysis will be populated here -->
    </div>

    <div id="revisionTimeline" style="display: none;">
      <!-- Timeline view will be populated here -->
    </div>
  </div>

  <!-- Export Data Tab -->
  <div class="tab-content" id="export">
    <div class="controls">
      <button id="exportComprehensiveBtn" class="primary-btn">Export Comprehensive CSV</button>
      <button id="exportUserSummaryBtn" class="success-btn">Export User Summary</button>
    </div>

    <div class="controls">
      <button id="exportTimelineBtn">Export Timeline CSV</button>
      <button id="exportJsonBtn">Export Full JSON</button>
    </div>

    <div id="exportStatus" class="revision-summary">
      <div>📊 <strong>Available Export Options:</strong></div>
      <div style="margin-top: 10px; font-size: 13px;">
        • <strong>Comprehensive CSV:</strong> All revisions with dates, users, and details<br>
        • <strong>User Summary:</strong> Contribution statistics by user<br>
        • <strong>Timeline CSV:</strong> Chronological revision timeline<br>
        • <strong>Full JSON:</strong> Complete analysis data
      </div>
    </div>
  </div>

  <!-- Help Tab -->
  <div class="tab-content" id="help">
    <div class="help-section">
      <div class="help-item">
        <div class="help-title">🚀 Getting Started</div>
        <div class="help-content">
          <ol class="help-steps">
            <li>Open any Google Docs document</li>
            <li>Click "Fetch ALL Revisions" in the Overview tab</li>
            <li>Go to "All Revisions" tab to analyze the data</li>
            <li>Export comprehensive CSV files from "Export Data" tab</li>
          </ol>
        </div>
      </div>

      <div class="help-item">
        <div class="help-title">📝 Comprehensive Revision Analysis</div>
        <div class="help-content">
          <ol class="help-steps">
            <li>Click "Fetch ALL Revisions" to load complete document history</li>
            <li>Go to "All Revisions" tab</li>
            <li>Click "Analyze All Revisions" for detailed breakdown</li>
            <li>Click "Show Timeline" to see chronological revision history</li>
          </ol>
        </div>
      </div>

      <div class="help-item">
        <div class="help-title">📊 Export Options</div>
        <div class="help-content">
          <ol class="help-steps">
            <li>Go to the "Export Data" tab</li>
            <li><strong>Comprehensive CSV:</strong> All revisions with dates, users, positions</li>
            <li><strong>User Summary:</strong> Contribution statistics by user</li>
            <li><strong>Timeline CSV:</strong> Chronological revision timeline</li>
            <li><strong>Full JSON:</strong> Complete analysis data for technical use</li>
          </ol>
        </div>
      </div>

      <div class="help-item">
        <div class="help-title">📈 What You Get</div>
        <div class="help-content">
          <strong>Comprehensive Data:</strong>
          <ul style="margin: 8px 0; padding-left: 20px;">
            <li>Every single revision with exact timestamps</li>
            <li>All contributors and their contributions</li>
            <li>Character counts and content positions</li>
            <li>Complete chronological timeline</li>
            <li>Detailed user statistics</li>
          </ul>
        </div>
      </div>

      <div class="help-item">
        <div class="help-title">🔧 Troubleshooting</div>
        <div class="help-content">
          <strong>Extension not working?</strong><br>
          • Refresh the Google Docs page<br>
          • Make sure you're on a document (not spreadsheet/slides)<br>
          • Check that the extension is enabled<br><br>

          <strong>No revision data?</strong><br>
          • Some documents may have restricted access to revision history<br>
          • Try refreshing and fetching again<br>
          • Make sure you have edit access to the document<br><br>

          <strong>Fetching takes long?</strong><br>
          • Large documents with many revisions take time to fetch<br>
          • The extension fetches ALL revisions in batches<br>
          • Wait for the process to complete for comprehensive data
        </div>
      </div>

      <div class="help-item">
        <div class="help-title">📞 Support & Contact</div>
        <div class="help-content">
          <strong>Need help or have questions?</strong><br><br>

          <strong>📧 Email Support:</strong><br>
          <a href="mailto:<EMAIL>" style="color: #1a73e8;"><EMAIL></a><br><br>

          <strong>🌐 Website:</strong><br>
          <a href="https://www.ecogo.ai" target="_blank" style="color: #1a73e8;">www.ecogo.ai</a><br><br>

          <strong>💡About ecogo.ai:</strong><br>
          Advanced AI-powered productivity tools for modern businesses. Make In Kerala, by Mallus !
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div class="footer">
    <div style="font-size: 11px; color: #5f6368; text-align: center; padding: 8px;">
      Powered by <strong style="color: #1a73e8;">ecogo.ai</strong> |
      <a href="mailto:<EMAIL>" style="color: #1a73e8; text-decoration: none;">Support</a> |
      <a href="https://www.ecogo.ai" target="_blank" style="color: #1a73e8; text-decoration: none;">www.ecogo.ai</a>
    </div>
  </div>

  <script src="revision-analyzer.js"></script>
  <script src="popup.js"></script>
  <script src="logo-handler.js"></script>
</body>
</html>